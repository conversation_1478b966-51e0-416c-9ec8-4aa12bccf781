package Main_fla
{
   import export.*;
   import flash.accessibility.*;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.filters.*;
   import flash.geom.*;
   import flash.media.*;
   import flash.net.*;
   import flash.net.drm.*;
   import flash.system.*;
   import flash.text.*;
   import flash.text.ime.*;
   import flash.ui.*;
   import flash.utils.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol136")]
   public dynamic class 元件3_18 extends MovieClip
   {
      public function 元件3_18()
      {
         super();
         addFrameScript(519,this.frame520);
      }
      
      internal function frame520() : *
      {
         if(this.parent)
         {
            CueMovieClip(this.parent).gotoNext();
         }
      }
   }
}

