package
{
   import base.*;
   import config.*;
   import flash.display.MovieClip;
   import flash.geom.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol53")]
   public class slopeObs extends MovieClip
   {
      public var isWall:MovieClip;
      
      private var equationX:Number;
      
      private var constX:Number;
      
      private var equationX2:Number;
      
      private var constX2:Number;
      
      private var sourceRotation:Number;
      
      private var minX:Number;
      
      private var maxX:Number;
      
      private var gc:Config;
      
      public function slopeObs()
      {
         super();
         this.gc = Config.getInstance();
         this.sourceRotation = this.rotation;
         this.rotation = 0;
         var _loc1_:Number = this.height;
         var _loc2_:Number = this.width;
         this.rotation = this.sourceRotation;
         this.equationX = Math.tan(this.rotation * Math.PI / 180);
         this.equationX2 = -1 / this.equationX;
         var _loc3_:Number = _loc1_ / 2 / Math.cos(Math.abs(this.rotation * Math.PI / 180));
         this.constX = -this.equationX * this.x + this.y - _loc3_;
         if(this.rotation < 0)
         {
            this.maxX = (this.y - this.height / 2 - this.constX) / this.equationX;
            this.minX = this.x - this.width / 2;
         }
         else
         {
            this.maxX = this.x + this.width / 2;
            this.minX = (this.y - this.height / 2 - this.constX) / this.equationX;
         }
      }
      
      public function setYByRole(param1:*) : Point
      {
         var _loc6_:Number = NaN;
         var _loc7_:Number = NaN;
         var _loc2_:Point = new Point();
         if(!param1.isWalkOrRun() && !param1.isBeAttacking() && !param1.isCanMoveWhenAttack())
         {
            return _loc2_;
         }
         var _loc3_:Rectangle = param1.getNextFrameXBounds();
         var _loc4_:Number = Math.abs(Math.cos(this.rotation * Math.PI / 180) * param1.speed.x);
         var _loc5_:Number = Math.tan(this.rotation * Math.PI / 180) * _loc4_;
         if(!this.isOutOfThisLine(param1) || this.isOutOfThisLine(param1) && this.getBounds(this.gc.gameSence).intersects(param1.getNextFrameXBounds()))
         {
            if(_loc3_.y + _loc3_.height - _loc5_ < this.y - this.height / 2)
            {
               _loc5_ = _loc3_.y + _loc3_.height - (this.y - this.height / 2);
            }
            if(param1.speed.x >= 0)
            {
               if(this.rotation < 0)
               {
                  _loc5_ = -Math.abs(_loc5_);
               }
               else
               {
                  _loc5_ = Math.abs(_loc5_);
               }
            }
            else
            {
               _loc4_ = -_loc4_;
               if(this.rotation < 0)
               {
                  _loc5_ = Math.abs(_loc5_);
               }
               else
               {
                  _loc5_ = -Math.abs(_loc5_);
               }
            }
            if(param1 is BaseHero)
            {
               if(param1.isCanMoveByStage())
               {
                  _loc2_.x = _loc4_;
                  _loc2_.y = _loc5_;
               }
            }
            else
            {
               _loc2_.x = _loc4_;
               _loc2_.y = _loc5_;
            }
         }
         return _loc2_;
      }
      
      public function isOutOfThisLine(param1:*) : Boolean
      {
         var _loc2_:Rectangle = param1.getNextFrameBounds();
         if(this.rotation < 0)
         {
            return !(_loc2_.x + _loc2_.width >= this.minX && _loc2_.x + _loc2_.width <= this.maxX);
         }
         return !(_loc2_.x >= this.minX && _loc2_.x <= this.maxX);
      }
   }
}

