package
{
   import PhysicsWorlds.*;
   import base.*;
   import com.greensock.*;
   import com.hexagonstar.util.debug.*;
   import config.*;
   import event.CommonEvent;
   import export.*;
   import export.huodong.*;
   import export.lose.*;
   import export.scene.*;
   import export.shop.*;
   import flash.display.*;
   import flash.events.*;
   import flash.net.*;
   import flash.utils.*;
   import loader.*;
   import manager.*;
   import my.*;
   import unit4399.events.*;
   
   public class GMain extends MovieClip
   {
      private static var _this:GMain;
      
      public static var _4399_function_store_id:String = "3885799f65acec467d97b4923caebaae";
      
      public static var _4399_function_ad_id:String = "92d6cef76cd06829e088932fe9fd819b";
      
      public static var _4399_function_score_id:String = "d8c8d4731a33a0a581edc746e73eadc7200";
      
      public static var serviceHold:* = null;
      
      private var gc:Config = new Config();
      
      private var _loader:Aloader;
      
      private var mainSence:Sprite;
      
      private var gm:GameMenu;
      
      public function GMain()
      {
         super();
         this._loader = new Aloader();
         this._loader.init();
         _this = this;
         this.gc.pWorld = new PhysicsWorld();
         this.gc.lc = new LocalConnection();
         this.gc.num8 = 9;
         this.mainSence = new Sprite();
         this.addChild(this.mainSence);
         this.addEventListener(Event.ADDED_TO_STAGE,this.added,false,0,true);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.removed,false,0,true);
      }
      
      public static function getInstance() : GMain
      {
         return _this;
      }
      
      public function setHold(param1:*) : void
      {
         serviceHold = param1;
      }
      
      private function saveProcess(param1:SaveEvent) : void
      {
         var e:SaveEvent = param1;
         switch(e.type)
         {
            case SaveEvent.SAVE_GET:
               Debug.trace("--SaveEvent.SAVE_GET--");
               Debug.trace("e.data:" + e.data);
               if(this.gc.loginAlert && this.gc.loginAlert.name == "LoginAlert" && Boolean(this.gc.loginAlert.parent))
               {
                  this.gc.loginAlert.parent.removeChild(this.gc.loginAlert);
                  this.gc.loginAlert = null;
               }
               if(e.data != null)
               {
                  if(!this.gc.loginAlert)
                  {
                     this.gc.loginAlert = new ComeBackAlert();
                     ComeBackAlert(this.gc.loginAlert).x = 470;
                     ComeBackAlert(this.gc.loginAlert).y = 295;
                     ComeBackAlert(this.gc.loginAlert).setUsername(this.gc.logInfo.name);
                     this.addChild(this.gc.loginAlert);
                     setTimeout(function():*
                     {
                        TweenMax.to(gc.loginAlert,2,{
                           "alpha":0,
                           "onComplete":function():*
                           {
                              if(Boolean(gc.loginAlert) && Boolean(gc.loginAlert.parent))
                              {
                                 gc.loginAlert.parent.removeChild(gc.loginAlert);
                                 gc.loginAlert = null;
                              }
                           }
                        });
                     },2000);
                  }
                  this.gc.memory.storageValue(e.data.index,e.data.data);
               }
               else if(!this.gc.loginAlert)
               {
                  this.gc.loginAlert = new FirstAlert();
                  FirstAlert(this.gc.loginAlert).x = 470;
                  FirstAlert(this.gc.loginAlert).y = 295;
                  FirstAlert(this.gc.loginAlert).setUsername(this.gc.logInfo.name);
                  this.addChild(this.gc.loginAlert);
                  setTimeout(function():*
                  {
                     TweenMax.to(gc.loginAlert,2,{
                        "alpha":0,
                        "onComplete":function():*
                        {
                           if(Boolean(gc.loginAlert) && Boolean(gc.loginAlert.parent))
                           {
                              gc.loginAlert.parent.removeChild(gc.loginAlert);
                              gc.loginAlert = null;
                           }
                        }
                     });
                  },2000);
               }
               this.gm.showAndHide();
               break;
            case SaveEvent.SAVE_SET:
               if(e.ret as Boolean == true)
               {
                  Debug.trace("======存档成功=======");
                  this.gc.showFloatTip("存档成功");
                  break;
               }
               Debug.trace("======存档失败=======");
               this.gc.showFloatTip("存档失败");
               break;
            case "logreturn":
               this.gc.logInfo = e.ret;
               Debug.trace("e.ret:" + e.ret);
               Debug.trace("e.ret.uid:" + e.ret.uid);
               Debug.trace("e.ret.name:" + e.ret.name);
               Debug.trace("gc.logInfo:" + this.gc.logInfo);
               if(this.gc.usernameHash == "")
               {
                  this.gc.usernameHash = e.ret.name;
               }
               if(e.ret)
               {
                  this.gm.doSelectNum();
                  break;
               }
         }
      }
      
      public function showProgress(param1:int, param2:int) : void
      {
         var _loc3_:LoadingBar = null;
         if(!this.getChildByName("showProgress"))
         {
            _loc3_ = new LoadingBar();
            _loc3_.name = "showProgress";
            _loc3_.setProcess(param1,param2);
            _loc3_.x = 470;
            _loc3_.y = 295;
            this.addChild(_loc3_);
         }
         else
         {
            LoadingBar(this.getChildByName("showProgress")).setProcess(param1,param2);
         }
      }
      
      public function processComplete() : void
      {
         var _loc1_:MovieClip = this.getChildByName("showProgress") as MovieClip;
         if(_loc1_)
         {
            this.removeChild(_loc1_);
            _loc1_ = null;
         }
      }
      
      public function switchSence(param1:String) : void
      {
         switch(param1)
         {
            case "startFighting":
               this.startGame(this.gc.curStage,this.gc.curLevel);
               break;
            case "GameMenu":
               this.showGameMenu();
               break;
            case "SelectRole":
               this.showSelectRolw();
               break;
            case "showStageMap":
               this.showStageMap();
               break;
            case "gameOver":
               this.showGameOver();
               break;
            case "OpenAnimation":
               this.showOpenAnimation();
         }
      }
      
      private function added(param1:*) : void
      {
         this.gc.eventManger.addEventListener("LoadOver",this.loaderOver);
         this.gc.eventManger.addEventListener("StartSelectRole",this.selectRole);
         this.gc.eventManger.addEventListener("SelectOver",this.SelectRoleOver);
         this.gc.eventManger.addEventListener("selectStageOver",this.selectStageOver);
         this.gc.eventManger.addEventListener("showBuySkill",this.showBuySkill);
         this.gc.eventManger.addEventListener("showNightYears",this.showNightYears);
         this.gc.eventManger.addEventListener("heroDead",this.heroDead);
         this.gc.eventManger.addEventListener("GameOver",this.gameOver);
         this.gc.eventManger.addEventListener("ReStart",this.reStart);
         this.gc.eventManger.addEventListener("ShowOpenAnimation",this.showOpenAnimation);
         this.gc.eventManger.addEventListener("ShowOpenAnimationOver",this.showOpenAnimationOver);
         this.gc.eventManger.addEventListener("showAboutUs",this.showAboutUs);
         this.gc.eventManger.addEventListener("GameHelp",this.gameHelp);
         stage.addEventListener("netSaveError",this.netSaveErrorHandler,false,0,true);
         stage.addEventListener("MVC_CLOSE_PANEL",this.closePanelHandler,false,0,true);
         stage.addEventListener(SaveEvent.SAVE_GET,this.saveProcess);
         stage.addEventListener(SaveEvent.SAVE_SET,this.saveProcess);
         stage.addEventListener("logreturn",this.saveProcess);
         stage.addEventListener("serverTimeEvent",this.onGetServerTimeHandler,false,0,true);
         stage.showDefaultContextMenu = false;
         stage.addEventListener(MouseEvent.RIGHT_CLICK,this.swallRightEvent);
         this.gc.stage = this.stage;
         BrowserInfo.getBrowserInfo();
         var _loc3_:Array = "".split("|");
         if(_loc3_[_loc3_.length - 1] == "4399.zm5.air")
         {
            this.gc.isAirGame = true;
         }
      }
      
      private function removed(param1:*) : void
      {
         this.gc.eventManger.removeEventListener("LoadOver",this.loaderOver);
         this.gc.eventManger.removeEventListener("StartSelectRole",this.selectRole);
         this.gc.eventManger.removeEventListener("SelectOver",this.SelectRoleOver);
         this.gc.eventManger.removeEventListener("selectStageOver",this.selectStageOver);
         this.gc.eventManger.removeEventListener("showBuySkill",this.showBuySkill);
         this.gc.eventManger.removeEventListener("showNightYears",this.showNightYears);
         this.gc.eventManger.removeEventListener("heroDead",this.heroDead);
         this.gc.eventManger.removeEventListener("GameOver",this.gameOver);
         this.gc.eventManger.removeEventListener("ReStart",this.reStart);
         this.gc.eventManger.removeEventListener("ShowOpenAnimation",this.showOpenAnimation);
         this.gc.eventManger.removeEventListener("ShowOpenAnimationOver",this.showOpenAnimationOver);
         this.gc.eventManger.removeEventListener("showAboutUs",this.showAboutUs);
         this.gc.eventManger.removeEventListener("GameHelp",this.gameHelp);
         stage.removeEventListener(MouseEvent.RIGHT_CLICK,this.swallRightEvent);
      }
      
      private function swallRightEvent(param1:*) : void
      {
      }
      
      private function onGetServerTimeHandler(param1:DataEvent) : void
      {
         this.gc.onGetServerTime(param1.data);
      }
      
      private function closePanelHandler(param1:Event) : void
      {
         if(Boolean(this.gc.loginAlert) && Boolean(this.gc.loginAlert.parent))
         {
            this.gc.loginAlert.parent.removeChild(this.gc.loginAlert);
            this.gc.loginAlert = null;
         }
      }
      
      private function showAboutUs(param1:CommonEvent) : void
      {
         var _loc2_:AboutUs = AUtils.getNewObj("export.AboutUs") as AboutUs;
         this.mainSence.addChild(_loc2_);
      }
      
      private function gameHelp(param1:CommonEvent) : void
      {
         var _loc2_:Help = AUtils.getNewObj("export.Help") as Help;
         this.mainSence.addChild(_loc2_);
      }
      
      private function showOpenAnimation() : void
      {
         var _loc1_:Opening = AUtils.getNewObj("export.scene.Opening") as Opening;
         this.mainSence.addChild(_loc1_);
      }
      
      private function showOpenAnimationOver(param1:*) : void
      {
         this.switchSence("showStageMap");
      }
      
      private function showBuySkill(param1:CommonEvent) : void
      {
         var _loc2_:BuySkill = AUtils.getNewObj("export.shop.BuySkill") as BuySkill;
         _loc2_.setCurrentState(param1.data.state);
         this.mainSence.addChild(_loc2_);
         _loc2_.name = "BuySkill";
      }
      
      private function showNightYears(param1:CommonEvent) : void
      {
         var _loc2_:NightYears = AUtils.getNewObj("export.huodong.NightYears") as NightYears;
         this.mainSence.addChild(_loc2_);
      }
      
      private function SelectRoleOver(param1:*) : void
      {
         if(Boolean(this.gc.memory.judgeSave()) || Boolean(this.gc.opening))
         {
            this.switchSence("showStageMap");
         }
         else
         {
            this.switchSence("OpenAnimation");
         }
      }
      
      private function showStageMap() : void
      {
         SoundManager.play("begin");
         var _loc1_:SelectPLace = AUtils.getNewObj("export.SelectPLace") as SelectPLace;
         this.mainSence.addChild(_loc1_);
      }
      
      private function selectStageOver(param1:*) : void
      {
         this.switchSence("startFighting");
      }
      
      private function showGameMenu() : void
      {
         if(this.gm == null)
         {
            this.gm = AUtils.getNewObj("export.GameMenu") as GameMenu;
         }
         this.mainSence.addChild(this.gm);
      }
      
      private function selectRole(param1:*) : *
      {
         this.switchSence("SelectRole");
      }
      
      private function showSelectRolw() : void
      {
         var _loc1_:SelectRole = AUtils.getNewObj("export.SelectRole") as SelectRole;
         this.mainSence.addChild(_loc1_);
      }
      
      private function gameOver(param1:*) : void
      {
         this.switchSence("gameOver");
      }
      
      private function showGameOver() : void
      {
         SoundManager.play("over");
         var _loc1_:GameFail = AUtils.getNewObj("export.lose.GameFail") as GameFail;
         this.mainSence.addChild(_loc1_);
      }
      
      private function reStart(param1:*) : void
      {
         this.switchSence("startFighting");
      }
      
      private function heroDead(param1:CommonEvent) : void
      {
         var _loc2_:* = AUtils.getNewObj("deadmb");
         _loc2_.x = param1.data.x;
         _loc2_.y = param1.data.y - 10;
         this.gc.gameSence.addChild(_loc2_);
         param1.data.destroy();
      }
      
      public function startGame(param1:uint, param2:uint) : void
      {
         new MainGame(this.mainSence).GameStart(this.gc.curStage,this.gc.curLevel);
      }
      
      private function doLoadOK() : void
      {
         var _loc2_:* = undefined;
         this.gc.pWorld.destroy();
         var _loc1_:int = 0;
         while(_loc1_ < this.numChildren)
         {
            _loc2_ = this.getChildAt(_loc1_);
            this.gc.pWorld.addSubObj(_loc2_);
            _loc1_++;
         }
         this.switchSence("GameMenu");
      }
      
      private function loaderOver(param1:*) : *
      {
         this.gc.eventManger.removeEventListener("loaderOver",this.loaderOver);
         AUtils.checkLoadOK(this,this.doLoadOK);
      }
      
      public function getMainSence() : Sprite
      {
         return this.mainSence;
      }
      
      private function netSaveErrorHandler(param1:Event) : void
      {
         this.gc.ts.setTxt("存档失败");
         this.addChild(this.gc.ts);
      }
   }
}

