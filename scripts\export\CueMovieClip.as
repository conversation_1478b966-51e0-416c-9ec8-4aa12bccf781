package export
{
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.net.*;
   
   public class Cue<PERSON>ovieClip extends MovieClip
   {
      public var cueText:MovieClip;
      
      public var playTimes:int = 0;
      
      public function CueMovieClip()
      {
         super();
         this.cueText.buttonMode = true;
         this.cueText.addEventListener(MouseEvent.CLICK,this.__gotoUrl,true,0,false);
         this.cueText.addEventListener(MouseEvent.MOUSE_OVER,this.__overText,true,0,false);
         this.cueText.addEventListener(MouseEvent.MOUSE_OUT,this.__outText,true,0,false);
      }
      
      private function __gotoUrl(param1:MouseEvent) : void
      {
         navigateToURL(new URLRequest("http://my.4399.com/space-59637201-do-thread-id-1664859-tagid-80615.html"),"_blank");
      }
      
      private function __overText(param1:MouseEvent) : void
      {
         this.cueText.stop();
      }
      
      private function __outText(param1:MouseEvent) : void
      {
         this.cueText.play();
      }
      
      public function gotoNext() : void
      {
         if(this.playTimes < 1)
         {
            this.cueText.gotoAndPlay(1);
            ++this.playTimes;
         }
         else
         {
            this.cueText.stop();
            if(this.parent)
            {
               this.parent.removeChild(this);
            }
         }
      }
   }
}

