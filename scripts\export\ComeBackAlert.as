package export
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol29")]
   public class ComeBackAlert extends MovieClip
   {
      public var userName:TextField;
      
      private var count:int = 0;
      
      public function ComeBackAlert()
      {
         super();
      }
      
      public function setUsername(param1:*) : void
      {
         this.userName.text = param1;
      }
   }
}

