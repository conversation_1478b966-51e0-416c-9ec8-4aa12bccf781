package base
{
   import flash.external.*;
   
   public class BrowserInfo
   {
      private static var _info:String;
      
      private static var _name:String;
      
      private static var _version:String;
      
      private static var _host:String;
      
      private static var _href:String;
      
      private static var _pathname:String;
      
      private static var _webTitle:String;
      
      public static var is_msie:<PERSON><PERSON>an = false;
      
      public static var is_firefox:<PERSON>olean = false;
      
      public static var is_opera:<PERSON>olean = false;
      
      public static var is_chrome:<PERSON>olean = false;
      
      public static var is_safari:Boolean = false;
      
      public static var is_other:<PERSON>olean = false;
      
      public static const MSIE:String = "msie";
      
      public static const FIREFOX:String = "firefox";
      
      public static const OPERA:String = "opera";
      
      public static const CHROME:String = "chrome";
      
      public static const SAFARI:String = "safari";
      
      public static const OTHER:String = "other";
      
      public function BrowserInfo()
      {
         super();
      }
      
      public static function getBrowserInfo() : void
      {
         if(ExternalInterface.available)
         {
            _info = ExternalInterface.call("eval","navigator.userAgent");
            if(_info)
            {
               _getBrowserInfo();
            }
         }
      }
      
      public static function get name() : String
      {
         return _name;
      }
      
      public static function get version() : String
      {
         return _version;
      }
      
      public static function get info() : String
      {
         return _info || false;
      }
      
      public static function get host() : String
      {
         _host = _jsReturn("window.location.host");
         return _host;
      }
      
      public static function get href() : String
      {
         _href = _jsReturn("window.location.href");
         return _href;
      }
      
      public static function get pathname() : String
      {
         _pathname = _jsReturn("window.location.pathname");
         return _pathname;
      }
      
      public static function get webTitle() : String
      {
         return _jsReturn("document.title");
      }
      
      private static function _jsReturn(param1:String) : String
      {
         if(ExternalInterface.available)
         {
            return ExternalInterface.call("eval",param1);
         }
         return "";
      }
      
      private static function _getBrowserInfo() : void
      {
         var _loc1_:RegExp = /.*(msie) ([\w.]+).*/;
         var _loc2_:RegExp = /.*(firefox)\/([\w.]+).*/;
         var _loc3_:RegExp = /(opera).+version\/([\w.]+)/;
         var _loc4_:RegExp = /.*(chrome)\/([\w.]+).*/;
         var _loc5_:RegExp = /.*version\/([\w.]+).*(safari).*/;
         _execInfo([_loc1_,_loc2_,_loc3_,_loc4_,_loc5_]);
      }
      
      private static function _execInfo(param1:Array) : void
      {
         var _loc3_:Array = null;
         var _loc4_:String = null;
         var _loc2_:String = _info.toLowerCase();
         for(_loc4_ in param1)
         {
            _loc3_ = param1[_loc4_].exec(_loc2_);
            if(_loc3_ != null)
            {
               if(_loc3_[1] == SAFARI)
               {
                  _name = _loc3_[2];
                  _version = _loc3_[1];
               }
               else
               {
                  _name = _loc3_[1];
                  _version = _loc3_[2];
               }
               BrowserInfo["is_" + _name] = true;
            }
         }
         if(_name == null || _name == "")
         {
            _name = OTHER;
            BrowserInfo.is_other = true;
         }
      }
   }
}

