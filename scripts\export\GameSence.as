package export
{
   import base.MonsterAppearPoint;
   import config.*;
   import export.scene.Thron;
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.utils.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol128")]
   public class GameSence extends MovieClip
   {
      public var __id0_:StopPoint;
      
      public var __id100_:MonsterAppearPoint;
      
      public var __id101_:MonsterAppearPoint;
      
      public var __id102_:StopPoint;
      
      public var __id103_:MonsterAppearPoint;
      
      public var __id104_:MonsterAppearPoint;
      
      public var __id105_:MonsterAppearPoint;
      
      public var __id106_:MonsterAppearPoint;
      
      public var __id107_:MonsterAppearPoint;
      
      public var __id108_:StopPoint;
      
      public var __id109_:MonsterAppearPoint;
      
      public var __id10_:MonsterAppearPoint;
      
      public var __id110_:MonsterAppearPoint;
      
      public var __id111_:MonsterAppearPoint;
      
      public var __id112_:MonsterAppearPoint;
      
      public var __id113_:MonsterAppearPoint;
      
      public var __id114_:MonsterAppearPoint;
      
      public var __id115_:MonsterAppearPoint;
      
      public var __id116_:MonsterAppearPoint;
      
      public var __id117_:MonsterAppearPoint;
      
      public var __id118_:MonsterAppearPoint;
      
      public var __id11_:StopPoint;
      
      public var __id123_:StopPoint;
      
      public var __id124_:MonsterAppearPoint;
      
      public var __id125_:MonsterAppearPoint;
      
      public var __id126_:MonsterAppearPoint;
      
      public var __id127_:MonsterAppearPoint;
      
      public var __id128_:MonsterAppearPoint;
      
      public var __id129_:MonsterAppearPoint;
      
      public var __id12_:MonsterAppearPoint;
      
      public var __id130_:MonsterAppearPoint;
      
      public var __id131_:StopPoint;
      
      public var __id132_:MonsterAppearPoint;
      
      public var __id133_:StopPoint;
      
      public var __id134_:MonsterAppearPoint;
      
      public var __id135_:MonsterAppearPoint;
      
      public var __id136_:StopPoint;
      
      public var __id137_:StopPoint;
      
      public var __id138_:MonsterAppearPoint;
      
      public var __id139_:MonsterAppearPoint;
      
      public var __id13_:MonsterAppearPoint;
      
      public var __id140_:MonsterAppearPoint;
      
      public var __id141_:MonsterAppearPoint;
      
      public var __id142_:MonsterAppearPoint;
      
      public var __id143_:MonsterAppearPoint;
      
      public var __id144_:StopPoint;
      
      public var __id145_:StopPoint;
      
      public var __id146_:StopPoint;
      
      public var __id147_:StopPoint;
      
      public var __id148_:StopPoint;
      
      public var __id149_:MonsterAppearPoint;
      
      public var __id14_:MonsterAppearPoint;
      
      public var __id15_:MonsterAppearPoint;
      
      public var __id16_:StopPoint;
      
      public var __id17_:MonsterAppearPoint;
      
      public var __id18_:MonsterAppearPoint;
      
      public var __id19_:StopPoint;
      
      public var __id1_:MonsterAppearPoint;
      
      public var __id20_:StopPoint;
      
      public var __id21_:MonsterAppearPoint;
      
      public var __id22_:MonsterAppearPoint;
      
      public var __id23_:MonsterAppearPoint;
      
      public var __id24_:MonsterAppearPoint;
      
      public var __id25_:MonsterAppearPoint;
      
      public var __id26_:MonsterAppearPoint;
      
      public var __id27_:MonsterAppearPoint;
      
      public var __id28_:MonsterAppearPoint;
      
      public var __id29_:MonsterAppearPoint;
      
      public var __id2_:MonsterAppearPoint;
      
      public var __id30_:MonsterAppearPoint;
      
      public var __id31_:MonsterAppearPoint;
      
      public var __id32_:MonsterAppearPoint;
      
      public var __id33_:MonsterAppearPoint;
      
      public var __id34_:MonsterAppearPoint;
      
      public var __id35_:StopPoint;
      
      public var __id36_:MonsterAppearPoint;
      
      public var __id37_:MonsterAppearPoint;
      
      public var __id38_:MonsterAppearPoint;
      
      public var __id39_:StopPoint;
      
      public var __id3_:MonsterAppearPoint;
      
      public var __id40_:MonsterAppearPoint;
      
      public var __id41_:StopPoint;
      
      public var __id42_:MonsterAppearPoint;
      
      public var __id43_:MonsterAppearPoint;
      
      public var __id44_:MonsterAppearPoint;
      
      public var __id45_:MonsterAppearPoint;
      
      public var __id46_:StopPoint;
      
      public var __id47_:MonsterAppearPoint;
      
      public var __id48_:MonsterAppearPoint;
      
      public var __id49_:MonsterAppearPoint;
      
      public var __id4_:StopPoint;
      
      public var __id50_:MonsterAppearPoint;
      
      public var __id51_:MonsterAppearPoint;
      
      public var __id52_:MonsterAppearPoint;
      
      public var __id53_:Thron;
      
      public var __id54_:Thron;
      
      public var __id55_:Thron;
      
      public var __id56_:Thron;
      
      public var __id57_:Thron;
      
      public var __id58_:Thron;
      
      public var __id59_:Thron;
      
      public var __id5_:MonsterAppearPoint;
      
      public var __id60_:Thron;
      
      public var __id61_:Thron;
      
      public var __id62_:Thron;
      
      public var __id63_:Thron;
      
      public var __id64_:Thron;
      
      public var __id65_:Thron;
      
      public var __id66_:Thron;
      
      public var __id67_:Thron;
      
      public var __id68_:Thron;
      
      public var __id69_:Thron;
      
      public var __id6_:StopPoint;
      
      public var __id70_:Thron;
      
      public var __id71_:Thron;
      
      public var __id72_:Thron;
      
      public var __id73_:Thron;
      
      public var __id74_:Thron;
      
      public var __id75_:Thron;
      
      public var __id76_:Thron;
      
      public var __id77_:Thron;
      
      public var __id78_:Thron;
      
      public var __id79_:Thron;
      
      public var __id7_:MonsterAppearPoint;
      
      public var __id80_:Thron;
      
      public var __id81_:Thron;
      
      public var __id82_:Thron;
      
      public var __id83_:Thron;
      
      public var __id84_:Thron;
      
      public var __id85_:Thron;
      
      public var __id86_:Thron;
      
      public var __id87_:Thron;
      
      public var __id88_:MonsterAppearPoint;
      
      public var __id89_:StopPoint;
      
      public var __id8_:MonsterAppearPoint;
      
      public var __id90_:MonsterAppearPoint;
      
      public var __id91_:MonsterAppearPoint;
      
      public var __id92_:MonsterAppearPoint;
      
      public var __id93_:MonsterAppearPoint;
      
      public var __id94_:StopPoint;
      
      public var __id95_:MonsterAppearPoint;
      
      public var __id96_:MonsterAppearPoint;
      
      public var __id97_:MonsterAppearPoint;
      
      public var __id98_:StopPoint;
      
      public var __id99_:MonsterAppearPoint;
      
      public var __id9_:MonsterAppearPoint;
      
      public var __setPropDict:Dictionary = new Dictionary(true);
      
      public var __lastFrameProp:int = -1;
      
      private var gc:Config;
      
      public var isWall:MovieClip;
      
      public var bgContainer:MovieClip;
      
      public function GameSence(param1:uint = 1, param2:uint = 1)
      {
         super();
         this.gc = Config.getInstance();
         if(this.gc.gameMode != 3)
         {
            this.gotoAndStop(param1 + "-" + param2);
         }
         else
         {
            this.gotoAndStop("vs");
         }
         this.addEventListener(Event.ADDED_TO_STAGE,this.__added);
         var _loc3_:MovieClip = AUtils.getNewObj("bgClips");
         _loc3_.y = 65;
         _loc3_.gotoAndStop(param1 + "-" + param2);
         this.bgContainer.addChildAt(_loc3_,0);
         addEventListener(Event.FRAME_CONSTRUCTED,this.__setProp_handler,false,0,true);
      }
      
      private function __added(param1:Event) : void
      {
         this.removeEventListener(Event.ADDED_TO_STAGE,this.__added);
         var _loc2_:int = 0;
         while(_loc2_ < this.numChildren)
         {
            this.gc.pWorld.addSubObj(this.getChildAt(_loc2_));
            _loc2_++;
         }
      }
      
      public function destroy() : void
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
      
      internal function __setProp___id0__主游戏_标志_0(param1:int) : *
      {
         if(this.__id0_ != null && param1 >= 1 && param1 <= 9 && (this.__setPropDict[this.__id0_] == undefined || !(int(this.__setPropDict[this.__id0_]) >= 1 && int(this.__setPropDict[this.__id0_]) <= 9)))
         {
            this.__setPropDict[this.__id0_] = param1;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.betweenRandL = 1150;
            this.__id0_.idx = 0;
            this.__id0_.isBoss = false;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0__主游戏_标志_9(param1:int) : *
      {
         if(this.__id0_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id0_] == undefined || !(int(this.__setPropDict[this.__id0_]) >= 10 && int(this.__setPropDict[this.__id0_]) <= 14)))
         {
            this.__setPropDict[this.__id0_] = param1;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.betweenRandL = 1150;
            this.__id0_.idx = 3;
            this.__id0_.isBoss = false;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0__主游戏_标志_14(param1:int) : *
      {
         if(this.__id0_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id0_] == undefined || !(int(this.__setPropDict[this.__id0_]) >= 15 && int(this.__setPropDict[this.__id0_]) <= 19)))
         {
            this.__setPropDict[this.__id0_] = param1;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.betweenRandL = 1150;
            this.__id0_.idx = 4;
            this.__id0_.isBoss = false;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0__主游戏_标志_19(param1:int) : *
      {
         if(this.__id0_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id0_] == undefined || !(int(this.__setPropDict[this.__id0_]) >= 20 && int(this.__setPropDict[this.__id0_]) <= 24)))
         {
            this.__setPropDict[this.__id0_] = param1;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.betweenRandL = 1150;
            this.__id0_.idx = 1;
            this.__id0_.isBoss = false;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0__主游戏_标志_24(param1:int) : *
      {
         if(this.__id0_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id0_] == undefined || !(int(this.__setPropDict[this.__id0_]) >= 25 && int(this.__setPropDict[this.__id0_]) <= 29)))
         {
            this.__setPropDict[this.__id0_] = param1;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.betweenRandL = 1150;
            this.__id0_.idx = 2;
            this.__id0_.isBoss = false;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id0__主游戏_标志_29(param1:int) : *
      {
         if(this.__id0_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id0_] == undefined || !(int(this.__setPropDict[this.__id0_]) >= 30 && int(this.__setPropDict[this.__id0_]) <= 34)))
         {
            this.__setPropDict[this.__id0_] = param1;
            try
            {
               this.__id0_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id0_.betweenRandL = 1150;
            this.__id0_.idx = 3;
            this.__id0_.isBoss = false;
            try
            {
               this.__id0_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1__主游戏_标志_0(param1:int) : *
      {
         if(this.__id1_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id1_] == undefined || !(int(this.__setPropDict[this.__id1_]) >= 1 && int(this.__setPropDict[this.__id1_]) <= 4)))
         {
            this.__setPropDict[this.__id1_] = param1;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 1;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 0;
            this.__id1_.totalNum = 3;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1__主游戏_标志_4(param1:int) : *
      {
         if(this.__id1_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id1_] == undefined || !(int(this.__setPropDict[this.__id1_]) >= 5 && int(this.__setPropDict[this.__id1_]) <= 9)))
         {
            this.__setPropDict[this.__id1_] = param1;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 2;
            this.__id1_.interval = 1;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 0;
            this.__id1_.totalNum = 2;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1__主游戏_标志_9(param1:int) : *
      {
         if(this.__id1_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id1_] == undefined || !(int(this.__setPropDict[this.__id1_]) >= 10 && int(this.__setPropDict[this.__id1_]) <= 14)))
         {
            this.__setPropDict[this.__id1_] = param1;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 5;
            this.__id1_.enemyType = 6;
            this.__id1_.interval = 2;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 4;
            this.__id1_.totalNum = 1;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1__主游戏_标志_14(param1:int) : *
      {
         if(this.__id1_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id1_] == undefined || !(int(this.__setPropDict[this.__id1_]) >= 15 && int(this.__setPropDict[this.__id1_]) <= 19)))
         {
            this.__setPropDict[this.__id1_] = param1;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 8;
            this.__id1_.interval = 3;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 3;
            this.__id1_.totalNum = 2;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1__主游戏_标志_19(param1:int) : *
      {
         if(this.__id1_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id1_] == undefined || !(int(this.__setPropDict[this.__id1_]) >= 20 && int(this.__setPropDict[this.__id1_]) <= 24)))
         {
            this.__setPropDict[this.__id1_] = param1;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 8;
            this.__id1_.interval = 2;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 1;
            this.__id1_.totalNum = 3;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1__主游戏_标志_24(param1:int) : *
      {
         if(this.__id1_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id1_] == undefined || !(int(this.__setPropDict[this.__id1_]) >= 25 && int(this.__setPropDict[this.__id1_]) <= 29)))
         {
            this.__setPropDict[this.__id1_] = param1;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 15;
            this.__id1_.interval = 2;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 2;
            this.__id1_.totalNum = 5;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1__主游戏_标志_29(param1:int) : *
      {
         if(this.__id1_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id1_] == undefined || !(int(this.__setPropDict[this.__id1_]) >= 30 && int(this.__setPropDict[this.__id1_]) <= 34)))
         {
            this.__setPropDict[this.__id1_] = param1;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 14;
            this.__id1_.interval = 2;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 3;
            this.__id1_.totalNum = 3;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1__主游戏_标志_34(param1:int) : *
      {
         if(this.__id1_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id1_] == undefined || !(int(this.__setPropDict[this.__id1_]) >= 35 && int(this.__setPropDict[this.__id1_]) <= 39)))
         {
            this.__setPropDict[this.__id1_] = param1;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 2;
            this.__id1_.enemyType = 20;
            this.__id1_.interval = 2;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 2;
            this.__id1_.totalNum = 1;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id1__主游戏_标志_39(param1:int) : *
      {
         if(this.__id1_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id1_] == undefined || !(int(this.__setPropDict[this.__id1_]) >= 40 && int(this.__setPropDict[this.__id1_]) <= 44)))
         {
            this.__setPropDict[this.__id1_] = param1;
            try
            {
               this.__id1_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id1_.delay = 12;
            this.__id1_.enemyType = 20;
            this.__id1_.interval = 2;
            this.__id1_.isRandom = false;
            this.__id1_.stopPointIdx = 2;
            this.__id1_.totalNum = 5;
            try
            {
               this.__id1_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2__主游戏_标志_0(param1:int) : *
      {
         if(this.__id2_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id2_] == undefined || !(int(this.__setPropDict[this.__id2_]) >= 1 && int(this.__setPropDict[this.__id2_]) <= 4)))
         {
            this.__setPropDict[this.__id2_] = param1;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 5;
            this.__id2_.enemyType = 2;
            this.__id2_.interval = 1;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 1;
            this.__id2_.totalNum = 1;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id2__主游戏_标志_4(param1:int) : *
      {
         if(this.__id2_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id2_] == undefined || !(int(this.__setPropDict[this.__id2_]) >= 5 && int(this.__setPropDict[this.__id2_]) <= 9)))
         {
            this.__setPropDict[this.__id2_] = param1;
            try
            {
               this.__id2_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id2_.delay = 5;
            this.__id2_.enemyType = 2;
            this.__id2_.interval = 1;
            this.__id2_.isRandom = false;
            this.__id2_.stopPointIdx = 1;
            this.__id2_.totalNum = 3;
            try
            {
               this.__id2_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3__主游戏_标志_0(param1:int) : *
      {
         if(this.__id3_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id3_] == undefined || !(int(this.__setPropDict[this.__id3_]) >= 1 && int(this.__setPropDict[this.__id3_]) <= 4)))
         {
            this.__setPropDict[this.__id3_] = param1;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 2;
            this.__id3_.enemyType = 1;
            this.__id3_.interval = 1;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 1;
            this.__id3_.totalNum = 5;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id3__主游戏_标志_4(param1:int) : *
      {
         if(this.__id3_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id3_] == undefined || !(int(this.__setPropDict[this.__id3_]) >= 5 && int(this.__setPropDict[this.__id3_]) <= 9)))
         {
            this.__setPropDict[this.__id3_] = param1;
            try
            {
               this.__id3_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id3_.delay = 3;
            this.__id3_.enemyType = 2;
            this.__id3_.interval = 2;
            this.__id3_.isRandom = false;
            this.__id3_.stopPointIdx = 3;
            this.__id3_.totalNum = 4;
            try
            {
               this.__id3_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4__主游戏_标志_0(param1:int) : *
      {
         if(this.__id4_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id4_] == undefined || !(int(this.__setPropDict[this.__id4_]) >= 1 && int(this.__setPropDict[this.__id4_]) <= 4)))
         {
            this.__setPropDict[this.__id4_] = param1;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.betweenRandL = 1150;
            this.__id4_.idx = 1;
            this.__id4_.isBoss = false;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id4__主游戏_标志_4(param1:int) : *
      {
         if(this.__id4_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id4_] == undefined || !(int(this.__setPropDict[this.__id4_]) >= 5 && int(this.__setPropDict[this.__id4_]) <= 9)))
         {
            this.__setPropDict[this.__id4_] = param1;
            try
            {
               this.__id4_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id4_.betweenRandL = 1150;
            this.__id4_.idx = 3;
            this.__id4_.isBoss = false;
            try
            {
               this.__id4_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5__主游戏_标志_0(param1:int) : *
      {
         if(this.__id5_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id5_] == undefined || !(int(this.__setPropDict[this.__id5_]) >= 1 && int(this.__setPropDict[this.__id5_]) <= 4)))
         {
            this.__setPropDict[this.__id5_] = param1;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 5;
            this.__id5_.enemyType = 2;
            this.__id5_.interval = 1;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 1;
            this.__id5_.totalNum = 1;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id5__主游戏_标志_4(param1:int) : *
      {
         if(this.__id5_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id5_] == undefined || !(int(this.__setPropDict[this.__id5_]) >= 5 && int(this.__setPropDict[this.__id5_]) <= 9)))
         {
            this.__setPropDict[this.__id5_] = param1;
            try
            {
               this.__id5_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id5_.delay = 4;
            this.__id5_.enemyType = 4;
            this.__id5_.interval = 2;
            this.__id5_.isRandom = false;
            this.__id5_.stopPointIdx = 4;
            this.__id5_.totalNum = 1;
            try
            {
               this.__id5_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6__主游戏_标志_0(param1:int) : *
      {
         if(this.__id6_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id6_] == undefined || !(int(this.__setPropDict[this.__id6_]) >= 1 && int(this.__setPropDict[this.__id6_]) <= 4)))
         {
            this.__setPropDict[this.__id6_] = param1;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.betweenRandL = 1150;
            this.__id6_.idx = 3;
            this.__id6_.isBoss = false;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id6__主游戏_标志_4(param1:int) : *
      {
         if(this.__id6_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id6_] == undefined || !(int(this.__setPropDict[this.__id6_]) >= 5 && int(this.__setPropDict[this.__id6_]) <= 9)))
         {
            this.__setPropDict[this.__id6_] = param1;
            try
            {
               this.__id6_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id6_.betweenRandL = 1150;
            this.__id6_.idx = 4;
            this.__id6_.isBoss = false;
            try
            {
               this.__id6_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id7__主游戏_标志_0(param1:int) : *
      {
         if(this.__id7_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id7_] == undefined || !(int(this.__setPropDict[this.__id7_]) >= 1 && int(this.__setPropDict[this.__id7_]) <= 4)))
         {
            this.__setPropDict[this.__id7_] = param1;
            try
            {
               this.__id7_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id7_.delay = 2;
            this.__id7_.enemyType = 1;
            this.__id7_.interval = 1;
            this.__id7_.isRandom = false;
            this.__id7_.stopPointIdx = 3;
            this.__id7_.totalNum = 5;
            try
            {
               this.__id7_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id8__主游戏_标志_0(param1:int) : *
      {
         if(this.__id8_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id8_] == undefined || !(int(this.__setPropDict[this.__id8_]) >= 1 && int(this.__setPropDict[this.__id8_]) <= 4)))
         {
            this.__setPropDict[this.__id8_] = param1;
            try
            {
               this.__id8_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id8_.delay = 3;
            this.__id8_.enemyType = 2;
            this.__id8_.interval = 1;
            this.__id8_.isRandom = false;
            this.__id8_.stopPointIdx = 3;
            this.__id8_.totalNum = 1;
            try
            {
               this.__id8_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id9__主游戏_标志_0(param1:int) : *
      {
         if(this.__id9_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id9_] == undefined || !(int(this.__setPropDict[this.__id9_]) >= 1 && int(this.__setPropDict[this.__id9_]) <= 4)))
         {
            this.__setPropDict[this.__id9_] = param1;
            try
            {
               this.__id9_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id9_.delay = 5;
            this.__id9_.enemyType = 2;
            this.__id9_.interval = 1;
            this.__id9_.isRandom = false;
            this.__id9_.stopPointIdx = 3;
            this.__id9_.totalNum = 1;
            try
            {
               this.__id9_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id10__主游戏_标志_0(param1:int) : *
      {
         if(this.__id10_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id10_] == undefined || !(int(this.__setPropDict[this.__id10_]) >= 1 && int(this.__setPropDict[this.__id10_]) <= 4)))
         {
            this.__setPropDict[this.__id10_] = param1;
            try
            {
               this.__id10_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id10_.delay = 4;
            this.__id10_.enemyType = 2;
            this.__id10_.interval = 2;
            this.__id10_.isRandom = false;
            this.__id10_.stopPointIdx = 2;
            this.__id10_.totalNum = 3;
            try
            {
               this.__id10_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id11__主游戏_标志_0(param1:int) : *
      {
         if(this.__id11_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id11_] == undefined || !(int(this.__setPropDict[this.__id11_]) >= 1 && int(this.__setPropDict[this.__id11_]) <= 4)))
         {
            this.__setPropDict[this.__id11_] = param1;
            try
            {
               this.__id11_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id11_.betweenRandL = 1150;
            this.__id11_.idx = 4;
            this.__id11_.isBoss = false;
            try
            {
               this.__id11_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id12__主游戏_标志_0(param1:int) : *
      {
         if(this.__id12_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id12_] == undefined || !(int(this.__setPropDict[this.__id12_]) >= 1 && int(this.__setPropDict[this.__id12_]) <= 4)))
         {
            this.__setPropDict[this.__id12_] = param1;
            try
            {
               this.__id12_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id12_.delay = 2;
            this.__id12_.enemyType = 2;
            this.__id12_.interval = 2;
            this.__id12_.isRandom = false;
            this.__id12_.stopPointIdx = 2;
            this.__id12_.totalNum = 1;
            try
            {
               this.__id12_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id13__主游戏_标志_0(param1:int) : *
      {
         if(this.__id13_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id13_] == undefined || !(int(this.__setPropDict[this.__id13_]) >= 1 && int(this.__setPropDict[this.__id13_]) <= 4)))
         {
            this.__setPropDict[this.__id13_] = param1;
            try
            {
               this.__id13_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id13_.delay = 2;
            this.__id13_.enemyType = 3;
            this.__id13_.interval = 2;
            this.__id13_.isRandom = false;
            this.__id13_.stopPointIdx = 4;
            this.__id13_.totalNum = 1;
            try
            {
               this.__id13_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id14__主游戏_标志_0(param1:int) : *
      {
         if(this.__id14_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id14_] == undefined || !(int(this.__setPropDict[this.__id14_]) >= 1 && int(this.__setPropDict[this.__id14_]) <= 4)))
         {
            this.__setPropDict[this.__id14_] = param1;
            try
            {
               this.__id14_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id14_.delay = 2;
            this.__id14_.enemyType = 2;
            this.__id14_.interval = 2;
            this.__id14_.isRandom = false;
            this.__id14_.stopPointIdx = 2;
            this.__id14_.totalNum = 1;
            try
            {
               this.__id14_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id15__主游戏_标志_0(param1:int) : *
      {
         if(this.__id15_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id15_] == undefined || !(int(this.__setPropDict[this.__id15_]) >= 1 && int(this.__setPropDict[this.__id15_]) <= 4)))
         {
            this.__setPropDict[this.__id15_] = param1;
            try
            {
               this.__id15_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id15_.delay = 2;
            this.__id15_.enemyType = 2;
            this.__id15_.interval = 2;
            this.__id15_.isRandom = false;
            this.__id15_.stopPointIdx = 2;
            this.__id15_.totalNum = 1;
            try
            {
               this.__id15_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id16__主游戏_标志_0(param1:int) : *
      {
         if(this.__id16_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id16_] == undefined || !(int(this.__setPropDict[this.__id16_]) >= 1 && int(this.__setPropDict[this.__id16_]) <= 4)))
         {
            this.__setPropDict[this.__id16_] = param1;
            try
            {
               this.__id16_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id16_.betweenRandL = 1150;
            this.__id16_.idx = 2;
            this.__id16_.isBoss = false;
            try
            {
               this.__id16_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id17__主游戏_标志_0(param1:int) : *
      {
         if(this.__id17_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id17_] == undefined || !(int(this.__setPropDict[this.__id17_]) >= 1 && int(this.__setPropDict[this.__id17_]) <= 4)))
         {
            this.__setPropDict[this.__id17_] = param1;
            try
            {
               this.__id17_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id17_.delay = 8;
            this.__id17_.enemyType = 2;
            this.__id17_.interval = 1;
            this.__id17_.isRandom = false;
            this.__id17_.stopPointIdx = 4;
            this.__id17_.totalNum = 5;
            try
            {
               this.__id17_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id18__主游戏_标志_0(param1:int) : *
      {
         if(this.__id18_ != null && param1 >= 1 && param1 <= 4 && (this.__setPropDict[this.__id18_] == undefined || !(int(this.__setPropDict[this.__id18_]) >= 1 && int(this.__setPropDict[this.__id18_]) <= 4)))
         {
            this.__setPropDict[this.__id18_] = param1;
            try
            {
               this.__id18_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id18_.delay = 3;
            this.__id18_.enemyType = 2;
            this.__id18_.interval = 1;
            this.__id18_.isRandom = false;
            this.__id18_.stopPointIdx = 4;
            this.__id18_.totalNum = 4;
            try
            {
               this.__id18_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id19__主游戏_标志_4(param1:int) : *
      {
         if(this.__id19_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id19_] == undefined || !(int(this.__setPropDict[this.__id19_]) >= 5 && int(this.__setPropDict[this.__id19_]) <= 9)))
         {
            this.__setPropDict[this.__id19_] = param1;
            try
            {
               this.__id19_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id19_.betweenRandL = 1150;
            this.__id19_.idx = 1;
            this.__id19_.isBoss = false;
            try
            {
               this.__id19_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id20__主游戏_标志_4(param1:int) : *
      {
         if(this.__id20_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id20_] == undefined || !(int(this.__setPropDict[this.__id20_]) >= 5 && int(this.__setPropDict[this.__id20_]) <= 9)))
         {
            this.__setPropDict[this.__id20_] = param1;
            try
            {
               this.__id20_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id20_.betweenRandL = 1150;
            this.__id20_.idx = 2;
            this.__id20_.isBoss = false;
            try
            {
               this.__id20_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id21__主游戏_标志_4(param1:int) : *
      {
         if(this.__id21_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id21_] == undefined || !(int(this.__setPropDict[this.__id21_]) >= 5 && int(this.__setPropDict[this.__id21_]) <= 9)))
         {
            this.__setPropDict[this.__id21_] = param1;
            try
            {
               this.__id21_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id21_.delay = 10;
            this.__id21_.enemyType = 3;
            this.__id21_.interval = 2;
            this.__id21_.isRandom = false;
            this.__id21_.stopPointIdx = 3;
            this.__id21_.totalNum = 3;
            try
            {
               this.__id21_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id22__主游戏_标志_4(param1:int) : *
      {
         if(this.__id22_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id22_] == undefined || !(int(this.__setPropDict[this.__id22_]) >= 5 && int(this.__setPropDict[this.__id22_]) <= 9)))
         {
            this.__setPropDict[this.__id22_] = param1;
            try
            {
               this.__id22_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id22_.delay = 5;
            this.__id22_.enemyType = 2;
            this.__id22_.interval = 1;
            this.__id22_.isRandom = false;
            this.__id22_.stopPointIdx = 3;
            this.__id22_.totalNum = 1;
            try
            {
               this.__id22_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id23__主游戏_标志_4(param1:int) : *
      {
         if(this.__id23_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id23_] == undefined || !(int(this.__setPropDict[this.__id23_]) >= 5 && int(this.__setPropDict[this.__id23_]) <= 9)))
         {
            this.__setPropDict[this.__id23_] = param1;
            try
            {
               this.__id23_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id23_.delay = 1;
            this.__id23_.enemyType = 1;
            this.__id23_.interval = 1;
            this.__id23_.isRandom = false;
            this.__id23_.stopPointIdx = 1;
            this.__id23_.totalNum = 5;
            try
            {
               this.__id23_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id24__主游戏_标志_4(param1:int) : *
      {
         if(this.__id24_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id24_] == undefined || !(int(this.__setPropDict[this.__id24_]) >= 5 && int(this.__setPropDict[this.__id24_]) <= 9)))
         {
            this.__setPropDict[this.__id24_] = param1;
            try
            {
               this.__id24_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id24_.delay = 4;
            this.__id24_.enemyType = 2;
            this.__id24_.interval = 2;
            this.__id24_.isRandom = false;
            this.__id24_.stopPointIdx = 2;
            this.__id24_.totalNum = 1;
            try
            {
               this.__id24_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id25__主游戏_标志_4(param1:int) : *
      {
         if(this.__id25_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id25_] == undefined || !(int(this.__setPropDict[this.__id25_]) >= 5 && int(this.__setPropDict[this.__id25_]) <= 9)))
         {
            this.__setPropDict[this.__id25_] = param1;
            try
            {
               this.__id25_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id25_.delay = 4;
            this.__id25_.enemyType = 2;
            this.__id25_.interval = 2;
            this.__id25_.isRandom = false;
            this.__id25_.stopPointIdx = 2;
            this.__id25_.totalNum = 1;
            try
            {
               this.__id25_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id26__主游戏_标志_4(param1:int) : *
      {
         if(this.__id26_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id26_] == undefined || !(int(this.__setPropDict[this.__id26_]) >= 5 && int(this.__setPropDict[this.__id26_]) <= 9)))
         {
            this.__setPropDict[this.__id26_] = param1;
            try
            {
               this.__id26_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id26_.delay = 1;
            this.__id26_.enemyType = 3;
            this.__id26_.interval = 2;
            this.__id26_.isRandom = false;
            this.__id26_.stopPointIdx = 2;
            this.__id26_.totalNum = 5;
            try
            {
               this.__id26_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id27__主游戏_标志_4(param1:int) : *
      {
         if(this.__id27_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id27_] == undefined || !(int(this.__setPropDict[this.__id27_]) >= 5 && int(this.__setPropDict[this.__id27_]) <= 9)))
         {
            this.__setPropDict[this.__id27_] = param1;
            try
            {
               this.__id27_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id27_.delay = 3;
            this.__id27_.enemyType = 1;
            this.__id27_.interval = 1;
            this.__id27_.isRandom = false;
            this.__id27_.stopPointIdx = 0;
            this.__id27_.totalNum = 4;
            try
            {
               this.__id27_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id28__主游戏_标志_4(param1:int) : *
      {
         if(this.__id28_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id28_] == undefined || !(int(this.__setPropDict[this.__id28_]) >= 5 && int(this.__setPropDict[this.__id28_]) <= 9)))
         {
            this.__setPropDict[this.__id28_] = param1;
            try
            {
               this.__id28_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id28_.delay = 5;
            this.__id28_.enemyType = 2;
            this.__id28_.interval = 1;
            this.__id28_.isRandom = false;
            this.__id28_.stopPointIdx = 1;
            this.__id28_.totalNum = 2;
            try
            {
               this.__id28_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id29__主游戏_标志_4(param1:int) : *
      {
         if(this.__id29_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id29_] == undefined || !(int(this.__setPropDict[this.__id29_]) >= 5 && int(this.__setPropDict[this.__id29_]) <= 9)))
         {
            this.__setPropDict[this.__id29_] = param1;
            try
            {
               this.__id29_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id29_.delay = 2;
            this.__id29_.enemyType = 2;
            this.__id29_.interval = 1;
            this.__id29_.isRandom = false;
            this.__id29_.stopPointIdx = 1;
            this.__id29_.totalNum = 2;
            try
            {
               this.__id29_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id30__主游戏_标志_4(param1:int) : *
      {
         if(this.__id30_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id30_] == undefined || !(int(this.__setPropDict[this.__id30_]) >= 5 && int(this.__setPropDict[this.__id30_]) <= 9)))
         {
            this.__setPropDict[this.__id30_] = param1;
            try
            {
               this.__id30_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id30_.delay = 2;
            this.__id30_.enemyType = 2;
            this.__id30_.interval = 1;
            this.__id30_.isRandom = false;
            this.__id30_.stopPointIdx = 1;
            this.__id30_.totalNum = 3;
            try
            {
               this.__id30_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id31__主游戏_标志_4(param1:int) : *
      {
         if(this.__id31_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id31_] == undefined || !(int(this.__setPropDict[this.__id31_]) >= 5 && int(this.__setPropDict[this.__id31_]) <= 9)))
         {
            this.__setPropDict[this.__id31_] = param1;
            try
            {
               this.__id31_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id31_.delay = 4;
            this.__id31_.enemyType = 2;
            this.__id31_.interval = 2;
            this.__id31_.isRandom = false;
            this.__id31_.stopPointIdx = 2;
            this.__id31_.totalNum = 1;
            try
            {
               this.__id31_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id32__主游戏_标志_4(param1:int) : *
      {
         if(this.__id32_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id32_] == undefined || !(int(this.__setPropDict[this.__id32_]) >= 5 && int(this.__setPropDict[this.__id32_]) <= 9)))
         {
            this.__setPropDict[this.__id32_] = param1;
            try
            {
               this.__id32_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id32_.delay = 7;
            this.__id32_.enemyType = 3;
            this.__id32_.interval = 2;
            this.__id32_.isRandom = false;
            this.__id32_.stopPointIdx = 2;
            this.__id32_.totalNum = 3;
            try
            {
               this.__id32_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id33__主游戏_标志_4(param1:int) : *
      {
         if(this.__id33_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id33_] == undefined || !(int(this.__setPropDict[this.__id33_]) >= 5 && int(this.__setPropDict[this.__id33_]) <= 9)))
         {
            this.__setPropDict[this.__id33_] = param1;
            try
            {
               this.__id33_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id33_.delay = 3;
            this.__id33_.enemyType = 2;
            this.__id33_.interval = 2;
            this.__id33_.isRandom = false;
            this.__id33_.stopPointIdx = 3;
            this.__id33_.totalNum = 4;
            try
            {
               this.__id33_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id34__主游戏_标志_4(param1:int) : *
      {
         if(this.__id34_ != null && param1 >= 5 && param1 <= 9 && (this.__setPropDict[this.__id34_] == undefined || !(int(this.__setPropDict[this.__id34_]) >= 5 && int(this.__setPropDict[this.__id34_]) <= 9)))
         {
            this.__setPropDict[this.__id34_] = param1;
            try
            {
               this.__id34_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id34_.delay = 10;
            this.__id34_.enemyType = 3;
            this.__id34_.interval = 2;
            this.__id34_.isRandom = false;
            this.__id34_.stopPointIdx = 3;
            this.__id34_.totalNum = 3;
            try
            {
               this.__id34_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id35__主游戏_背景_9(param1:int) : *
      {
         if(this.__id35_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id35_] == undefined || !(int(this.__setPropDict[this.__id35_]) >= 10 && int(this.__setPropDict[this.__id35_]) <= 14)))
         {
            this.__setPropDict[this.__id35_] = param1;
            try
            {
               this.__id35_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id35_.betweenRandL = 1150;
            this.__id35_.idx = 0;
            this.__id35_.isBoss = false;
            try
            {
               this.__id35_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id36__主游戏_背景_9(param1:int) : *
      {
         if(this.__id36_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id36_] == undefined || !(int(this.__setPropDict[this.__id36_]) >= 10 && int(this.__setPropDict[this.__id36_]) <= 14)))
         {
            this.__setPropDict[this.__id36_] = param1;
            try
            {
               this.__id36_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id36_.delay = 1;
            this.__id36_.enemyType = 2;
            this.__id36_.interval = 2;
            this.__id36_.isRandom = false;
            this.__id36_.stopPointIdx = 0;
            this.__id36_.totalNum = 4;
            try
            {
               this.__id36_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id37__主游戏_背景_9(param1:int) : *
      {
         if(this.__id37_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id37_] == undefined || !(int(this.__setPropDict[this.__id37_]) >= 10 && int(this.__setPropDict[this.__id37_]) <= 14)))
         {
            this.__setPropDict[this.__id37_] = param1;
            try
            {
               this.__id37_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id37_.delay = 2;
            this.__id37_.enemyType = 3;
            this.__id37_.interval = 2;
            this.__id37_.isRandom = false;
            this.__id37_.stopPointIdx = 1;
            this.__id37_.totalNum = 5;
            try
            {
               this.__id37_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id38__主游戏_背景_9(param1:int) : *
      {
         if(this.__id38_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id38_] == undefined || !(int(this.__setPropDict[this.__id38_]) >= 10 && int(this.__setPropDict[this.__id38_]) <= 14)))
         {
            this.__setPropDict[this.__id38_] = param1;
            try
            {
               this.__id38_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id38_.delay = 2;
            this.__id38_.enemyType = 3;
            this.__id38_.interval = 2;
            this.__id38_.isRandom = false;
            this.__id38_.stopPointIdx = 1;
            this.__id38_.totalNum = 3;
            try
            {
               this.__id38_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id39__主游戏_背景_9(param1:int) : *
      {
         if(this.__id39_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id39_] == undefined || !(int(this.__setPropDict[this.__id39_]) >= 10 && int(this.__setPropDict[this.__id39_]) <= 14)))
         {
            this.__setPropDict[this.__id39_] = param1;
            try
            {
               this.__id39_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id39_.betweenRandL = 1150;
            this.__id39_.idx = 1;
            this.__id39_.isBoss = false;
            try
            {
               this.__id39_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id40__主游戏_背景_9(param1:int) : *
      {
         if(this.__id40_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id40_] == undefined || !(int(this.__setPropDict[this.__id40_]) >= 10 && int(this.__setPropDict[this.__id40_]) <= 14)))
         {
            this.__setPropDict[this.__id40_] = param1;
            try
            {
               this.__id40_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id40_.delay = 5;
            this.__id40_.enemyType = 2;
            this.__id40_.interval = 3;
            this.__id40_.isRandom = false;
            this.__id40_.stopPointIdx = 1;
            this.__id40_.totalNum = 3;
            try
            {
               this.__id40_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id41__主游戏_背景_9(param1:int) : *
      {
         if(this.__id41_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id41_] == undefined || !(int(this.__setPropDict[this.__id41_]) >= 10 && int(this.__setPropDict[this.__id41_]) <= 14)))
         {
            this.__setPropDict[this.__id41_] = param1;
            try
            {
               this.__id41_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id41_.betweenRandL = 1150;
            this.__id41_.idx = 2;
            this.__id41_.isBoss = false;
            try
            {
               this.__id41_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id42__主游戏_背景_9(param1:int) : *
      {
         if(this.__id42_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id42_] == undefined || !(int(this.__setPropDict[this.__id42_]) >= 10 && int(this.__setPropDict[this.__id42_]) <= 14)))
         {
            this.__setPropDict[this.__id42_] = param1;
            try
            {
               this.__id42_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id42_.delay = 8;
            this.__id42_.enemyType = 3;
            this.__id42_.interval = 1;
            this.__id42_.isRandom = false;
            this.__id42_.stopPointIdx = 3;
            this.__id42_.totalNum = 3;
            try
            {
               this.__id42_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id43__主游戏_背景_9(param1:int) : *
      {
         if(this.__id43_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id43_] == undefined || !(int(this.__setPropDict[this.__id43_]) >= 10 && int(this.__setPropDict[this.__id43_]) <= 14)))
         {
            this.__setPropDict[this.__id43_] = param1;
            try
            {
               this.__id43_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id43_.delay = 3;
            this.__id43_.enemyType = 3;
            this.__id43_.interval = 1;
            this.__id43_.isRandom = false;
            this.__id43_.stopPointIdx = 3;
            this.__id43_.totalNum = 3;
            try
            {
               this.__id43_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id44__主游戏_背景_9(param1:int) : *
      {
         if(this.__id44_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id44_] == undefined || !(int(this.__setPropDict[this.__id44_]) >= 10 && int(this.__setPropDict[this.__id44_]) <= 14)))
         {
            this.__setPropDict[this.__id44_] = param1;
            try
            {
               this.__id44_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id44_.delay = 1;
            this.__id44_.enemyType = 3;
            this.__id44_.interval = 2;
            this.__id44_.isRandom = false;
            this.__id44_.stopPointIdx = 3;
            this.__id44_.totalNum = 4;
            try
            {
               this.__id44_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id45__主游戏_背景_9(param1:int) : *
      {
         if(this.__id45_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id45_] == undefined || !(int(this.__setPropDict[this.__id45_]) >= 10 && int(this.__setPropDict[this.__id45_]) <= 14)))
         {
            this.__setPropDict[this.__id45_] = param1;
            try
            {
               this.__id45_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id45_.delay = 4;
            this.__id45_.enemyType = 2;
            this.__id45_.interval = 2;
            this.__id45_.isRandom = false;
            this.__id45_.stopPointIdx = 1;
            this.__id45_.totalNum = 3;
            try
            {
               this.__id45_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id46__主游戏_背景_9(param1:int) : *
      {
         if(this.__id46_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id46_] == undefined || !(int(this.__setPropDict[this.__id46_]) >= 10 && int(this.__setPropDict[this.__id46_]) <= 14)))
         {
            this.__setPropDict[this.__id46_] = param1;
            try
            {
               this.__id46_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id46_.betweenRandL = 1150;
            this.__id46_.idx = 4;
            this.__id46_.isBoss = false;
            try
            {
               this.__id46_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id47__主游戏_背景_9(param1:int) : *
      {
         if(this.__id47_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id47_] == undefined || !(int(this.__setPropDict[this.__id47_]) >= 10 && int(this.__setPropDict[this.__id47_]) <= 14)))
         {
            this.__setPropDict[this.__id47_] = param1;
            try
            {
               this.__id47_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id47_.delay = 3;
            this.__id47_.enemyType = 3;
            this.__id47_.interval = 2;
            this.__id47_.isRandom = false;
            this.__id47_.stopPointIdx = 4;
            this.__id47_.totalNum = 3;
            try
            {
               this.__id47_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id48__主游戏_背景_9(param1:int) : *
      {
         if(this.__id48_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id48_] == undefined || !(int(this.__setPropDict[this.__id48_]) >= 10 && int(this.__setPropDict[this.__id48_]) <= 14)))
         {
            this.__setPropDict[this.__id48_] = param1;
            try
            {
               this.__id48_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id48_.delay = 2;
            this.__id48_.enemyType = 3;
            this.__id48_.interval = 2;
            this.__id48_.isRandom = false;
            this.__id48_.stopPointIdx = 2;
            this.__id48_.totalNum = 3;
            try
            {
               this.__id48_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id49__主游戏_背景_9(param1:int) : *
      {
         if(this.__id49_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id49_] == undefined || !(int(this.__setPropDict[this.__id49_]) >= 10 && int(this.__setPropDict[this.__id49_]) <= 14)))
         {
            this.__setPropDict[this.__id49_] = param1;
            try
            {
               this.__id49_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id49_.delay = 1;
            this.__id49_.enemyType = 3;
            this.__id49_.interval = 2;
            this.__id49_.isRandom = false;
            this.__id49_.stopPointIdx = 4;
            this.__id49_.totalNum = 3;
            try
            {
               this.__id49_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id50__主游戏_背景_9(param1:int) : *
      {
         if(this.__id50_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id50_] == undefined || !(int(this.__setPropDict[this.__id50_]) >= 10 && int(this.__setPropDict[this.__id50_]) <= 14)))
         {
            this.__setPropDict[this.__id50_] = param1;
            try
            {
               this.__id50_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id50_.delay = 6;
            this.__id50_.enemyType = 3;
            this.__id50_.interval = 2;
            this.__id50_.isRandom = false;
            this.__id50_.stopPointIdx = 2;
            this.__id50_.totalNum = 2;
            try
            {
               this.__id50_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id51__主游戏_背景_9(param1:int) : *
      {
         if(this.__id51_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id51_] == undefined || !(int(this.__setPropDict[this.__id51_]) >= 10 && int(this.__setPropDict[this.__id51_]) <= 14)))
         {
            this.__setPropDict[this.__id51_] = param1;
            try
            {
               this.__id51_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id51_.delay = 2;
            this.__id51_.enemyType = 3;
            this.__id51_.interval = 2;
            this.__id51_.isRandom = false;
            this.__id51_.stopPointIdx = 3;
            this.__id51_.totalNum = 5;
            try
            {
               this.__id51_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id52__主游戏_背景_9(param1:int) : *
      {
         if(this.__id52_ != null && param1 >= 10 && param1 <= 14 && (this.__setPropDict[this.__id52_] == undefined || !(int(this.__setPropDict[this.__id52_]) >= 10 && int(this.__setPropDict[this.__id52_]) <= 14)))
         {
            this.__setPropDict[this.__id52_] = param1;
            try
            {
               this.__id52_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id52_.delay = 4;
            this.__id52_.enemyType = 3;
            this.__id52_.interval = 3;
            this.__id52_.isRandom = false;
            this.__id52_.stopPointIdx = 2;
            this.__id52_.totalNum = 4;
            try
            {
               this.__id52_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id53__主游戏_背景_14(param1:int) : *
      {
         if(this.__id53_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id53_] == undefined || !(int(this.__setPropDict[this.__id53_]) >= 15 && int(this.__setPropDict[this.__id53_]) <= 19)))
         {
            this.__setPropDict[this.__id53_] = param1;
            try
            {
               this.__id53_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id53_.isUp = false;
            try
            {
               this.__id53_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id53__主游戏_背景_19(param1:int) : *
      {
         if(this.__id53_ != null && param1 >= 20 && param1 <= 29 && (this.__setPropDict[this.__id53_] == undefined || !(int(this.__setPropDict[this.__id53_]) >= 20 && int(this.__setPropDict[this.__id53_]) <= 29)))
         {
            this.__setPropDict[this.__id53_] = param1;
            try
            {
               this.__id53_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id53_.isUp = true;
            try
            {
               this.__id53_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id54__主游戏_背景_14(param1:int) : *
      {
         if(this.__id54_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id54_] == undefined || !(int(this.__setPropDict[this.__id54_]) >= 15 && int(this.__setPropDict[this.__id54_]) <= 19)))
         {
            this.__setPropDict[this.__id54_] = param1;
            try
            {
               this.__id54_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id54_.isUp = false;
            try
            {
               this.__id54_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id54__主游戏_背景_19(param1:int) : *
      {
         if(this.__id54_ != null && param1 >= 20 && param1 <= 29 && (this.__setPropDict[this.__id54_] == undefined || !(int(this.__setPropDict[this.__id54_]) >= 20 && int(this.__setPropDict[this.__id54_]) <= 29)))
         {
            this.__setPropDict[this.__id54_] = param1;
            try
            {
               this.__id54_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id54_.isUp = true;
            try
            {
               this.__id54_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id55__主游戏_背景_14(param1:int) : *
      {
         if(this.__id55_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id55_] == undefined || !(int(this.__setPropDict[this.__id55_]) >= 15 && int(this.__setPropDict[this.__id55_]) <= 19)))
         {
            this.__setPropDict[this.__id55_] = param1;
            try
            {
               this.__id55_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id55_.isUp = false;
            try
            {
               this.__id55_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id55__主游戏_背景_19(param1:int) : *
      {
         if(this.__id55_ != null && param1 >= 20 && param1 <= 29 && (this.__setPropDict[this.__id55_] == undefined || !(int(this.__setPropDict[this.__id55_]) >= 20 && int(this.__setPropDict[this.__id55_]) <= 29)))
         {
            this.__setPropDict[this.__id55_] = param1;
            try
            {
               this.__id55_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id55_.isUp = true;
            try
            {
               this.__id55_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id56__主游戏_背景_14(param1:int) : *
      {
         if(this.__id56_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id56_] == undefined || !(int(this.__setPropDict[this.__id56_]) >= 15 && int(this.__setPropDict[this.__id56_]) <= 19)))
         {
            this.__setPropDict[this.__id56_] = param1;
            try
            {
               this.__id56_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id56_.isUp = false;
            try
            {
               this.__id56_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id56__主游戏_背景_19(param1:int) : *
      {
         if(this.__id56_ != null && param1 >= 20 && param1 <= 29 && (this.__setPropDict[this.__id56_] == undefined || !(int(this.__setPropDict[this.__id56_]) >= 20 && int(this.__setPropDict[this.__id56_]) <= 29)))
         {
            this.__setPropDict[this.__id56_] = param1;
            try
            {
               this.__id56_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id56_.isUp = true;
            try
            {
               this.__id56_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id57__主游戏_背景_14(param1:int) : *
      {
         if(this.__id57_ != null && param1 >= 15 && param1 <= 29 && (this.__setPropDict[this.__id57_] == undefined || !(int(this.__setPropDict[this.__id57_]) >= 15 && int(this.__setPropDict[this.__id57_]) <= 29)))
         {
            this.__setPropDict[this.__id57_] = param1;
            try
            {
               this.__id57_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id57_.isUp = true;
            try
            {
               this.__id57_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id58__主游戏_背景_14(param1:int) : *
      {
         if(this.__id58_ != null && param1 >= 15 && param1 <= 29 && (this.__setPropDict[this.__id58_] == undefined || !(int(this.__setPropDict[this.__id58_]) >= 15 && int(this.__setPropDict[this.__id58_]) <= 29)))
         {
            this.__setPropDict[this.__id58_] = param1;
            try
            {
               this.__id58_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id58_.isUp = true;
            try
            {
               this.__id58_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id59__主游戏_背景_14(param1:int) : *
      {
         if(this.__id59_ != null && param1 >= 15 && param1 <= 29 && (this.__setPropDict[this.__id59_] == undefined || !(int(this.__setPropDict[this.__id59_]) >= 15 && int(this.__setPropDict[this.__id59_]) <= 29)))
         {
            this.__setPropDict[this.__id59_] = param1;
            try
            {
               this.__id59_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id59_.isUp = true;
            try
            {
               this.__id59_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id60__主游戏_背景_14(param1:int) : *
      {
         if(this.__id60_ != null && param1 >= 15 && param1 <= 29 && (this.__setPropDict[this.__id60_] == undefined || !(int(this.__setPropDict[this.__id60_]) >= 15 && int(this.__setPropDict[this.__id60_]) <= 29)))
         {
            this.__setPropDict[this.__id60_] = param1;
            try
            {
               this.__id60_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id60_.isUp = true;
            try
            {
               this.__id60_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id61__主游戏_背景_14(param1:int) : *
      {
         if(this.__id61_ != null && param1 >= 15 && param1 <= 29 && (this.__setPropDict[this.__id61_] == undefined || !(int(this.__setPropDict[this.__id61_]) >= 15 && int(this.__setPropDict[this.__id61_]) <= 29)))
         {
            this.__setPropDict[this.__id61_] = param1;
            try
            {
               this.__id61_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id61_.isUp = true;
            try
            {
               this.__id61_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id62__主游戏_背景_14(param1:int) : *
      {
         if(this.__id62_ != null && param1 >= 15 && param1 <= 29 && (this.__setPropDict[this.__id62_] == undefined || !(int(this.__setPropDict[this.__id62_]) >= 15 && int(this.__setPropDict[this.__id62_]) <= 29)))
         {
            this.__setPropDict[this.__id62_] = param1;
            try
            {
               this.__id62_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id62_.isUp = true;
            try
            {
               this.__id62_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id63__主游戏_背景_14(param1:int) : *
      {
         if(this.__id63_ != null && param1 >= 15 && param1 <= 29 && (this.__setPropDict[this.__id63_] == undefined || !(int(this.__setPropDict[this.__id63_]) >= 15 && int(this.__setPropDict[this.__id63_]) <= 29)))
         {
            this.__setPropDict[this.__id63_] = param1;
            try
            {
               this.__id63_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id63_.isUp = true;
            try
            {
               this.__id63_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id64__主游戏_背景_14(param1:int) : *
      {
         if(this.__id64_ != null && param1 >= 15 && param1 <= 29 && (this.__setPropDict[this.__id64_] == undefined || !(int(this.__setPropDict[this.__id64_]) >= 15 && int(this.__setPropDict[this.__id64_]) <= 29)))
         {
            this.__setPropDict[this.__id64_] = param1;
            try
            {
               this.__id64_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id64_.isUp = true;
            try
            {
               this.__id64_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id65__主游戏_背景_14(param1:int) : *
      {
         if(this.__id65_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id65_] == undefined || !(int(this.__setPropDict[this.__id65_]) >= 15 && int(this.__setPropDict[this.__id65_]) <= 19)))
         {
            this.__setPropDict[this.__id65_] = param1;
            try
            {
               this.__id65_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id65_.isUp = false;
            try
            {
               this.__id65_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id65__主游戏_背景_19(param1:int) : *
      {
         if(this.__id65_ != null && param1 >= 20 && param1 <= 29 && (this.__setPropDict[this.__id65_] == undefined || !(int(this.__setPropDict[this.__id65_]) >= 20 && int(this.__setPropDict[this.__id65_]) <= 29)))
         {
            this.__setPropDict[this.__id65_] = param1;
            try
            {
               this.__id65_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id65_.isUp = true;
            try
            {
               this.__id65_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id66__主游戏_背景_14(param1:int) : *
      {
         if(this.__id66_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id66_] == undefined || !(int(this.__setPropDict[this.__id66_]) >= 15 && int(this.__setPropDict[this.__id66_]) <= 24)))
         {
            this.__setPropDict[this.__id66_] = param1;
            try
            {
               this.__id66_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id66_.isUp = false;
            try
            {
               this.__id66_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id66__主游戏_背景_24(param1:int) : *
      {
         if(this.__id66_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id66_] == undefined || !(int(this.__setPropDict[this.__id66_]) >= 25 && int(this.__setPropDict[this.__id66_]) <= 29)))
         {
            this.__setPropDict[this.__id66_] = param1;
            try
            {
               this.__id66_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id66_.isUp = true;
            try
            {
               this.__id66_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id67__主游戏_背景_14(param1:int) : *
      {
         if(this.__id67_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id67_] == undefined || !(int(this.__setPropDict[this.__id67_]) >= 15 && int(this.__setPropDict[this.__id67_]) <= 24)))
         {
            this.__setPropDict[this.__id67_] = param1;
            try
            {
               this.__id67_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id67_.isUp = false;
            try
            {
               this.__id67_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id67__主游戏_背景_24(param1:int) : *
      {
         if(this.__id67_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id67_] == undefined || !(int(this.__setPropDict[this.__id67_]) >= 25 && int(this.__setPropDict[this.__id67_]) <= 29)))
         {
            this.__setPropDict[this.__id67_] = param1;
            try
            {
               this.__id67_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id67_.isUp = true;
            try
            {
               this.__id67_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id68__主游戏_背景_14(param1:int) : *
      {
         if(this.__id68_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id68_] == undefined || !(int(this.__setPropDict[this.__id68_]) >= 15 && int(this.__setPropDict[this.__id68_]) <= 24)))
         {
            this.__setPropDict[this.__id68_] = param1;
            try
            {
               this.__id68_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id68_.isUp = false;
            try
            {
               this.__id68_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id68__主游戏_背景_24(param1:int) : *
      {
         if(this.__id68_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id68_] == undefined || !(int(this.__setPropDict[this.__id68_]) >= 25 && int(this.__setPropDict[this.__id68_]) <= 29)))
         {
            this.__setPropDict[this.__id68_] = param1;
            try
            {
               this.__id68_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id68_.isUp = true;
            try
            {
               this.__id68_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id69__主游戏_背景_14(param1:int) : *
      {
         if(this.__id69_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id69_] == undefined || !(int(this.__setPropDict[this.__id69_]) >= 15 && int(this.__setPropDict[this.__id69_]) <= 24)))
         {
            this.__setPropDict[this.__id69_] = param1;
            try
            {
               this.__id69_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id69_.isUp = false;
            try
            {
               this.__id69_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id69__主游戏_背景_24(param1:int) : *
      {
         if(this.__id69_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id69_] == undefined || !(int(this.__setPropDict[this.__id69_]) >= 25 && int(this.__setPropDict[this.__id69_]) <= 29)))
         {
            this.__setPropDict[this.__id69_] = param1;
            try
            {
               this.__id69_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id69_.isUp = true;
            try
            {
               this.__id69_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id70__主游戏_背景_14(param1:int) : *
      {
         if(this.__id70_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id70_] == undefined || !(int(this.__setPropDict[this.__id70_]) >= 15 && int(this.__setPropDict[this.__id70_]) <= 24)))
         {
            this.__setPropDict[this.__id70_] = param1;
            try
            {
               this.__id70_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id70_.isUp = false;
            try
            {
               this.__id70_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id70__主游戏_背景_24(param1:int) : *
      {
         if(this.__id70_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id70_] == undefined || !(int(this.__setPropDict[this.__id70_]) >= 25 && int(this.__setPropDict[this.__id70_]) <= 29)))
         {
            this.__setPropDict[this.__id70_] = param1;
            try
            {
               this.__id70_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id70_.isUp = true;
            try
            {
               this.__id70_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id71__主游戏_背景_14(param1:int) : *
      {
         if(this.__id71_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id71_] == undefined || !(int(this.__setPropDict[this.__id71_]) >= 15 && int(this.__setPropDict[this.__id71_]) <= 24)))
         {
            this.__setPropDict[this.__id71_] = param1;
            try
            {
               this.__id71_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id71_.isUp = false;
            try
            {
               this.__id71_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id71__主游戏_背景_24(param1:int) : *
      {
         if(this.__id71_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id71_] == undefined || !(int(this.__setPropDict[this.__id71_]) >= 25 && int(this.__setPropDict[this.__id71_]) <= 29)))
         {
            this.__setPropDict[this.__id71_] = param1;
            try
            {
               this.__id71_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id71_.isUp = true;
            try
            {
               this.__id71_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id72__主游戏_背景_14(param1:int) : *
      {
         if(this.__id72_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id72_] == undefined || !(int(this.__setPropDict[this.__id72_]) >= 15 && int(this.__setPropDict[this.__id72_]) <= 19)))
         {
            this.__setPropDict[this.__id72_] = param1;
            try
            {
               this.__id72_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id72_.isUp = true;
            try
            {
               this.__id72_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id72__主游戏_背景_19(param1:int) : *
      {
         if(this.__id72_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id72_] == undefined || !(int(this.__setPropDict[this.__id72_]) >= 20 && int(this.__setPropDict[this.__id72_]) <= 24)))
         {
            this.__setPropDict[this.__id72_] = param1;
            try
            {
               this.__id72_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id72_.isUp = false;
            try
            {
               this.__id72_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id72__主游戏_背景_24(param1:int) : *
      {
         if(this.__id72_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id72_] == undefined || !(int(this.__setPropDict[this.__id72_]) >= 25 && int(this.__setPropDict[this.__id72_]) <= 29)))
         {
            this.__setPropDict[this.__id72_] = param1;
            try
            {
               this.__id72_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id72_.isUp = true;
            try
            {
               this.__id72_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id73__主游戏_背景_14(param1:int) : *
      {
         if(this.__id73_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id73_] == undefined || !(int(this.__setPropDict[this.__id73_]) >= 15 && int(this.__setPropDict[this.__id73_]) <= 19)))
         {
            this.__setPropDict[this.__id73_] = param1;
            try
            {
               this.__id73_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id73_.isUp = true;
            try
            {
               this.__id73_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id73__主游戏_背景_19(param1:int) : *
      {
         if(this.__id73_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id73_] == undefined || !(int(this.__setPropDict[this.__id73_]) >= 20 && int(this.__setPropDict[this.__id73_]) <= 24)))
         {
            this.__setPropDict[this.__id73_] = param1;
            try
            {
               this.__id73_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id73_.isUp = false;
            try
            {
               this.__id73_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id73__主游戏_背景_24(param1:int) : *
      {
         if(this.__id73_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id73_] == undefined || !(int(this.__setPropDict[this.__id73_]) >= 25 && int(this.__setPropDict[this.__id73_]) <= 29)))
         {
            this.__setPropDict[this.__id73_] = param1;
            try
            {
               this.__id73_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id73_.isUp = true;
            try
            {
               this.__id73_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id74__主游戏_背景_14(param1:int) : *
      {
         if(this.__id74_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id74_] == undefined || !(int(this.__setPropDict[this.__id74_]) >= 15 && int(this.__setPropDict[this.__id74_]) <= 19)))
         {
            this.__setPropDict[this.__id74_] = param1;
            try
            {
               this.__id74_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id74_.isUp = true;
            try
            {
               this.__id74_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id74__主游戏_背景_19(param1:int) : *
      {
         if(this.__id74_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id74_] == undefined || !(int(this.__setPropDict[this.__id74_]) >= 20 && int(this.__setPropDict[this.__id74_]) <= 24)))
         {
            this.__setPropDict[this.__id74_] = param1;
            try
            {
               this.__id74_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id74_.isUp = false;
            try
            {
               this.__id74_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id74__主游戏_背景_24(param1:int) : *
      {
         if(this.__id74_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id74_] == undefined || !(int(this.__setPropDict[this.__id74_]) >= 25 && int(this.__setPropDict[this.__id74_]) <= 29)))
         {
            this.__setPropDict[this.__id74_] = param1;
            try
            {
               this.__id74_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id74_.isUp = true;
            try
            {
               this.__id74_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id75__主游戏_背景_14(param1:int) : *
      {
         if(this.__id75_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id75_] == undefined || !(int(this.__setPropDict[this.__id75_]) >= 15 && int(this.__setPropDict[this.__id75_]) <= 19)))
         {
            this.__setPropDict[this.__id75_] = param1;
            try
            {
               this.__id75_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id75_.isUp = true;
            try
            {
               this.__id75_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id75__主游戏_背景_19(param1:int) : *
      {
         if(this.__id75_ != null && param1 >= 20 && param1 <= 29 && (this.__setPropDict[this.__id75_] == undefined || !(int(this.__setPropDict[this.__id75_]) >= 20 && int(this.__setPropDict[this.__id75_]) <= 29)))
         {
            this.__setPropDict[this.__id75_] = param1;
            try
            {
               this.__id75_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id75_.isUp = false;
            try
            {
               this.__id75_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id76__主游戏_背景_14(param1:int) : *
      {
         if(this.__id76_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id76_] == undefined || !(int(this.__setPropDict[this.__id76_]) >= 15 && int(this.__setPropDict[this.__id76_]) <= 19)))
         {
            this.__setPropDict[this.__id76_] = param1;
            try
            {
               this.__id76_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id76_.isUp = true;
            try
            {
               this.__id76_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id76__主游戏_背景_19(param1:int) : *
      {
         if(this.__id76_ != null && param1 >= 20 && param1 <= 29 && (this.__setPropDict[this.__id76_] == undefined || !(int(this.__setPropDict[this.__id76_]) >= 20 && int(this.__setPropDict[this.__id76_]) <= 29)))
         {
            this.__setPropDict[this.__id76_] = param1;
            try
            {
               this.__id76_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id76_.isUp = false;
            try
            {
               this.__id76_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id77__主游戏_背景_14(param1:int) : *
      {
         if(this.__id77_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id77_] == undefined || !(int(this.__setPropDict[this.__id77_]) >= 15 && int(this.__setPropDict[this.__id77_]) <= 19)))
         {
            this.__setPropDict[this.__id77_] = param1;
            try
            {
               this.__id77_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id77_.isUp = true;
            try
            {
               this.__id77_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id77__主游戏_背景_19(param1:int) : *
      {
         if(this.__id77_ != null && param1 >= 20 && param1 <= 29 && (this.__setPropDict[this.__id77_] == undefined || !(int(this.__setPropDict[this.__id77_]) >= 20 && int(this.__setPropDict[this.__id77_]) <= 29)))
         {
            this.__setPropDict[this.__id77_] = param1;
            try
            {
               this.__id77_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id77_.isUp = false;
            try
            {
               this.__id77_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id78__主游戏_背景_14(param1:int) : *
      {
         if(this.__id78_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id78_] == undefined || !(int(this.__setPropDict[this.__id78_]) >= 15 && int(this.__setPropDict[this.__id78_]) <= 19)))
         {
            this.__setPropDict[this.__id78_] = param1;
            try
            {
               this.__id78_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id78_.isUp = true;
            try
            {
               this.__id78_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id78__主游戏_背景_19(param1:int) : *
      {
         if(this.__id78_ != null && param1 >= 20 && param1 <= 29 && (this.__setPropDict[this.__id78_] == undefined || !(int(this.__setPropDict[this.__id78_]) >= 20 && int(this.__setPropDict[this.__id78_]) <= 29)))
         {
            this.__setPropDict[this.__id78_] = param1;
            try
            {
               this.__id78_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id78_.isUp = false;
            try
            {
               this.__id78_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id79__主游戏_背景_14(param1:int) : *
      {
         if(this.__id79_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id79_] == undefined || !(int(this.__setPropDict[this.__id79_]) >= 15 && int(this.__setPropDict[this.__id79_]) <= 19)))
         {
            this.__setPropDict[this.__id79_] = param1;
            try
            {
               this.__id79_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id79_.isUp = true;
            try
            {
               this.__id79_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id79__主游戏_背景_19(param1:int) : *
      {
         if(this.__id79_ != null && param1 >= 20 && param1 <= 29 && (this.__setPropDict[this.__id79_] == undefined || !(int(this.__setPropDict[this.__id79_]) >= 20 && int(this.__setPropDict[this.__id79_]) <= 29)))
         {
            this.__setPropDict[this.__id79_] = param1;
            try
            {
               this.__id79_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id79_.isUp = false;
            try
            {
               this.__id79_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id80__主游戏_背景_14(param1:int) : *
      {
         if(this.__id80_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id80_] == undefined || !(int(this.__setPropDict[this.__id80_]) >= 15 && int(this.__setPropDict[this.__id80_]) <= 24)))
         {
            this.__setPropDict[this.__id80_] = param1;
            try
            {
               this.__id80_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id80_.isUp = true;
            try
            {
               this.__id80_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id80__主游戏_背景_24(param1:int) : *
      {
         if(this.__id80_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id80_] == undefined || !(int(this.__setPropDict[this.__id80_]) >= 25 && int(this.__setPropDict[this.__id80_]) <= 29)))
         {
            this.__setPropDict[this.__id80_] = param1;
            try
            {
               this.__id80_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id80_.isUp = false;
            try
            {
               this.__id80_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id81__主游戏_背景_14(param1:int) : *
      {
         if(this.__id81_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id81_] == undefined || !(int(this.__setPropDict[this.__id81_]) >= 15 && int(this.__setPropDict[this.__id81_]) <= 24)))
         {
            this.__setPropDict[this.__id81_] = param1;
            try
            {
               this.__id81_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id81_.isUp = true;
            try
            {
               this.__id81_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id81__主游戏_背景_24(param1:int) : *
      {
         if(this.__id81_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id81_] == undefined || !(int(this.__setPropDict[this.__id81_]) >= 25 && int(this.__setPropDict[this.__id81_]) <= 29)))
         {
            this.__setPropDict[this.__id81_] = param1;
            try
            {
               this.__id81_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id81_.isUp = false;
            try
            {
               this.__id81_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id82__主游戏_背景_14(param1:int) : *
      {
         if(this.__id82_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id82_] == undefined || !(int(this.__setPropDict[this.__id82_]) >= 15 && int(this.__setPropDict[this.__id82_]) <= 24)))
         {
            this.__setPropDict[this.__id82_] = param1;
            try
            {
               this.__id82_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id82_.isUp = true;
            try
            {
               this.__id82_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id82__主游戏_背景_24(param1:int) : *
      {
         if(this.__id82_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id82_] == undefined || !(int(this.__setPropDict[this.__id82_]) >= 25 && int(this.__setPropDict[this.__id82_]) <= 29)))
         {
            this.__setPropDict[this.__id82_] = param1;
            try
            {
               this.__id82_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id82_.isUp = false;
            try
            {
               this.__id82_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id83__主游戏_背景_14(param1:int) : *
      {
         if(this.__id83_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id83_] == undefined || !(int(this.__setPropDict[this.__id83_]) >= 15 && int(this.__setPropDict[this.__id83_]) <= 24)))
         {
            this.__setPropDict[this.__id83_] = param1;
            try
            {
               this.__id83_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id83_.isUp = true;
            try
            {
               this.__id83_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id83__主游戏_背景_24(param1:int) : *
      {
         if(this.__id83_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id83_] == undefined || !(int(this.__setPropDict[this.__id83_]) >= 25 && int(this.__setPropDict[this.__id83_]) <= 29)))
         {
            this.__setPropDict[this.__id83_] = param1;
            try
            {
               this.__id83_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id83_.isUp = false;
            try
            {
               this.__id83_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id84__主游戏_背景_14(param1:int) : *
      {
         if(this.__id84_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id84_] == undefined || !(int(this.__setPropDict[this.__id84_]) >= 15 && int(this.__setPropDict[this.__id84_]) <= 24)))
         {
            this.__setPropDict[this.__id84_] = param1;
            try
            {
               this.__id84_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id84_.isUp = true;
            try
            {
               this.__id84_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id84__主游戏_背景_24(param1:int) : *
      {
         if(this.__id84_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id84_] == undefined || !(int(this.__setPropDict[this.__id84_]) >= 25 && int(this.__setPropDict[this.__id84_]) <= 29)))
         {
            this.__setPropDict[this.__id84_] = param1;
            try
            {
               this.__id84_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id84_.isUp = false;
            try
            {
               this.__id84_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id85__主游戏_背景_14(param1:int) : *
      {
         if(this.__id85_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id85_] == undefined || !(int(this.__setPropDict[this.__id85_]) >= 15 && int(this.__setPropDict[this.__id85_]) <= 24)))
         {
            this.__setPropDict[this.__id85_] = param1;
            try
            {
               this.__id85_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id85_.isUp = true;
            try
            {
               this.__id85_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id85__主游戏_背景_24(param1:int) : *
      {
         if(this.__id85_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id85_] == undefined || !(int(this.__setPropDict[this.__id85_]) >= 25 && int(this.__setPropDict[this.__id85_]) <= 29)))
         {
            this.__setPropDict[this.__id85_] = param1;
            try
            {
               this.__id85_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id85_.isUp = false;
            try
            {
               this.__id85_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id86__主游戏_背景_14(param1:int) : *
      {
         if(this.__id86_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id86_] == undefined || !(int(this.__setPropDict[this.__id86_]) >= 15 && int(this.__setPropDict[this.__id86_]) <= 24)))
         {
            this.__setPropDict[this.__id86_] = param1;
            try
            {
               this.__id86_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id86_.isUp = true;
            try
            {
               this.__id86_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id86__主游戏_背景_24(param1:int) : *
      {
         if(this.__id86_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id86_] == undefined || !(int(this.__setPropDict[this.__id86_]) >= 25 && int(this.__setPropDict[this.__id86_]) <= 29)))
         {
            this.__setPropDict[this.__id86_] = param1;
            try
            {
               this.__id86_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id86_.isUp = false;
            try
            {
               this.__id86_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id87__主游戏_背景_14(param1:int) : *
      {
         if(this.__id87_ != null && param1 >= 15 && param1 <= 24 && (this.__setPropDict[this.__id87_] == undefined || !(int(this.__setPropDict[this.__id87_]) >= 15 && int(this.__setPropDict[this.__id87_]) <= 24)))
         {
            this.__setPropDict[this.__id87_] = param1;
            try
            {
               this.__id87_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id87_.isUp = true;
            try
            {
               this.__id87_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id87__主游戏_背景_24(param1:int) : *
      {
         if(this.__id87_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id87_] == undefined || !(int(this.__setPropDict[this.__id87_]) >= 25 && int(this.__setPropDict[this.__id87_]) <= 29)))
         {
            this.__setPropDict[this.__id87_] = param1;
            try
            {
               this.__id87_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id87_.isUp = false;
            try
            {
               this.__id87_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id88__主游戏_标志_14(param1:int) : *
      {
         if(this.__id88_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id88_] == undefined || !(int(this.__setPropDict[this.__id88_]) >= 15 && int(this.__setPropDict[this.__id88_]) <= 19)))
         {
            this.__setPropDict[this.__id88_] = param1;
            try
            {
               this.__id88_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id88_.delay = 4;
            this.__id88_.enemyType = 8;
            this.__id88_.interval = 5;
            this.__id88_.isRandom = false;
            this.__id88_.stopPointIdx = 4;
            this.__id88_.totalNum = 999;
            try
            {
               this.__id88_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id88__主游戏_标志_19(param1:int) : *
      {
         if(this.__id88_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id88_] == undefined || !(int(this.__setPropDict[this.__id88_]) >= 20 && int(this.__setPropDict[this.__id88_]) <= 24)))
         {
            this.__setPropDict[this.__id88_] = param1;
            try
            {
               this.__id88_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id88_.delay = 2;
            this.__id88_.enemyType = 13;
            this.__id88_.interval = 2;
            this.__id88_.isRandom = false;
            this.__id88_.stopPointIdx = 0;
            this.__id88_.totalNum = 3;
            try
            {
               this.__id88_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id88__主游戏_标志_24(param1:int) : *
      {
         if(this.__id88_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id88_] == undefined || !(int(this.__setPropDict[this.__id88_]) >= 25 && int(this.__setPropDict[this.__id88_]) <= 29)))
         {
            this.__setPropDict[this.__id88_] = param1;
            try
            {
               this.__id88_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id88_.delay = 2;
            this.__id88_.enemyType = 15;
            this.__id88_.interval = 2;
            this.__id88_.isRandom = false;
            this.__id88_.stopPointIdx = 0;
            this.__id88_.totalNum = 5;
            try
            {
               this.__id88_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id88__主游戏_标志_29(param1:int) : *
      {
         if(this.__id88_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id88_] == undefined || !(int(this.__setPropDict[this.__id88_]) >= 30 && int(this.__setPropDict[this.__id88_]) <= 34)))
         {
            this.__setPropDict[this.__id88_] = param1;
            try
            {
               this.__id88_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id88_.delay = 10;
            this.__id88_.enemyType = 16;
            this.__id88_.interval = 2;
            this.__id88_.isRandom = false;
            this.__id88_.stopPointIdx = 0;
            this.__id88_.totalNum = 5;
            try
            {
               this.__id88_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id88__主游戏_标志_34(param1:int) : *
      {
         if(this.__id88_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id88_] == undefined || !(int(this.__setPropDict[this.__id88_]) >= 35 && int(this.__setPropDict[this.__id88_]) <= 39)))
         {
            this.__setPropDict[this.__id88_] = param1;
            try
            {
               this.__id88_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id88_.delay = 12;
            this.__id88_.enemyType = 14;
            this.__id88_.interval = 2;
            this.__id88_.isRandom = false;
            this.__id88_.stopPointIdx = 0;
            this.__id88_.totalNum = 5;
            try
            {
               this.__id88_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id88__主游戏_标志_39(param1:int) : *
      {
         if(this.__id88_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id88_] == undefined || !(int(this.__setPropDict[this.__id88_]) >= 40 && int(this.__setPropDict[this.__id88_]) <= 44)))
         {
            this.__setPropDict[this.__id88_] = param1;
            try
            {
               this.__id88_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id88_.delay = 2;
            this.__id88_.enemyType = 20;
            this.__id88_.interval = 2;
            this.__id88_.isRandom = false;
            this.__id88_.stopPointIdx = 0;
            this.__id88_.totalNum = 2;
            try
            {
               this.__id88_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id88__主游戏_标志_44(param1:int) : *
      {
         if(this.__id88_ != null && param1 >= 45 && param1 <= 48 && (this.__setPropDict[this.__id88_] == undefined || !(int(this.__setPropDict[this.__id88_]) >= 45 && int(this.__setPropDict[this.__id88_]) <= 48)))
         {
            this.__setPropDict[this.__id88_] = param1;
            try
            {
               this.__id88_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id88_.delay = 2;
            this.__id88_.enemyType = 22;
            this.__id88_.interval = 1;
            this.__id88_.isRandom = false;
            this.__id88_.stopPointIdx = 1;
            this.__id88_.totalNum = 1;
            try
            {
               this.__id88_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id89__主游戏_标志_14(param1:int) : *
      {
         if(this.__id89_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id89_] == undefined || !(int(this.__setPropDict[this.__id89_]) >= 15 && int(this.__setPropDict[this.__id89_]) <= 19)))
         {
            this.__setPropDict[this.__id89_] = param1;
            try
            {
               this.__id89_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id89_.betweenRandL = 1150;
            this.__id89_.idx = 3;
            this.__id89_.isBoss = false;
            try
            {
               this.__id89_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id89__主游戏_标志_19(param1:int) : *
      {
         if(this.__id89_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id89_] == undefined || !(int(this.__setPropDict[this.__id89_]) >= 20 && int(this.__setPropDict[this.__id89_]) <= 24)))
         {
            this.__setPropDict[this.__id89_] = param1;
            try
            {
               this.__id89_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id89_.betweenRandL = 1150;
            this.__id89_.idx = 2;
            this.__id89_.isBoss = false;
            try
            {
               this.__id89_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id89__主游戏_标志_24(param1:int) : *
      {
         if(this.__id89_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id89_] == undefined || !(int(this.__setPropDict[this.__id89_]) >= 25 && int(this.__setPropDict[this.__id89_]) <= 29)))
         {
            this.__setPropDict[this.__id89_] = param1;
            try
            {
               this.__id89_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id89_.betweenRandL = 1150;
            this.__id89_.idx = 3;
            this.__id89_.isBoss = false;
            try
            {
               this.__id89_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id90__主游戏_标志_14(param1:int) : *
      {
         if(this.__id90_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id90_] == undefined || !(int(this.__setPropDict[this.__id90_]) >= 15 && int(this.__setPropDict[this.__id90_]) <= 19)))
         {
            this.__setPropDict[this.__id90_] = param1;
            try
            {
               this.__id90_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id90_.delay = 2;
            this.__id90_.enemyType = 8;
            this.__id90_.interval = 3;
            this.__id90_.isRandom = false;
            this.__id90_.stopPointIdx = 3;
            this.__id90_.totalNum = 2;
            try
            {
               this.__id90_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id90__主游戏_标志_19(param1:int) : *
      {
         if(this.__id90_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id90_] == undefined || !(int(this.__setPropDict[this.__id90_]) >= 20 && int(this.__setPropDict[this.__id90_]) <= 24)))
         {
            this.__setPropDict[this.__id90_] = param1;
            try
            {
               this.__id90_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id90_.delay = 2;
            this.__id90_.enemyType = 8;
            this.__id90_.interval = 2;
            this.__id90_.isRandom = false;
            this.__id90_.stopPointIdx = 2;
            this.__id90_.totalNum = 5;
            try
            {
               this.__id90_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id90__主游戏_标志_24(param1:int) : *
      {
         if(this.__id90_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id90_] == undefined || !(int(this.__setPropDict[this.__id90_]) >= 25 && int(this.__setPropDict[this.__id90_]) <= 29)))
         {
            this.__setPropDict[this.__id90_] = param1;
            try
            {
               this.__id90_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id90_.delay = 2;
            this.__id90_.enemyType = 15;
            this.__id90_.interval = 2;
            this.__id90_.isRandom = false;
            this.__id90_.stopPointIdx = 3;
            this.__id90_.totalNum = 4;
            try
            {
               this.__id90_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id91__主游戏_标志_14(param1:int) : *
      {
         if(this.__id91_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id91_] == undefined || !(int(this.__setPropDict[this.__id91_]) >= 15 && int(this.__setPropDict[this.__id91_]) <= 19)))
         {
            this.__setPropDict[this.__id91_] = param1;
            try
            {
               this.__id91_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id91_.delay = 2;
            this.__id91_.enemyType = 8;
            this.__id91_.interval = 3;
            this.__id91_.isRandom = false;
            this.__id91_.stopPointIdx = 3;
            this.__id91_.totalNum = 2;
            try
            {
               this.__id91_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id91__主游戏_标志_19(param1:int) : *
      {
         if(this.__id91_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id91_] == undefined || !(int(this.__setPropDict[this.__id91_]) >= 20 && int(this.__setPropDict[this.__id91_]) <= 24)))
         {
            this.__setPropDict[this.__id91_] = param1;
            try
            {
               this.__id91_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id91_.delay = 10;
            this.__id91_.enemyType = 13;
            this.__id91_.interval = 2;
            this.__id91_.isRandom = false;
            this.__id91_.stopPointIdx = 2;
            this.__id91_.totalNum = 3;
            try
            {
               this.__id91_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id91__主游戏_标志_24(param1:int) : *
      {
         if(this.__id91_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id91_] == undefined || !(int(this.__setPropDict[this.__id91_]) >= 25 && int(this.__setPropDict[this.__id91_]) <= 29)))
         {
            this.__setPropDict[this.__id91_] = param1;
            try
            {
               this.__id91_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id91_.delay = 2;
            this.__id91_.enemyType = 13;
            this.__id91_.interval = 2;
            this.__id91_.isRandom = false;
            this.__id91_.stopPointIdx = 3;
            this.__id91_.totalNum = 2;
            try
            {
               this.__id91_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id92__主游戏_标志_14(param1:int) : *
      {
         if(this.__id92_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id92_] == undefined || !(int(this.__setPropDict[this.__id92_]) >= 15 && int(this.__setPropDict[this.__id92_]) <= 19)))
         {
            this.__setPropDict[this.__id92_] = param1;
            try
            {
               this.__id92_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id92_.delay = 12;
            this.__id92_.enemyType = 8;
            this.__id92_.interval = 2;
            this.__id92_.isRandom = false;
            this.__id92_.stopPointIdx = 3;
            this.__id92_.totalNum = 3;
            try
            {
               this.__id92_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id92__主游戏_标志_19(param1:int) : *
      {
         if(this.__id92_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id92_] == undefined || !(int(this.__setPropDict[this.__id92_]) >= 20 && int(this.__setPropDict[this.__id92_]) <= 24)))
         {
            this.__setPropDict[this.__id92_] = param1;
            try
            {
               this.__id92_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id92_.delay = 10;
            this.__id92_.enemyType = 7;
            this.__id92_.interval = 2;
            this.__id92_.isRandom = false;
            this.__id92_.stopPointIdx = 2;
            this.__id92_.totalNum = 3;
            try
            {
               this.__id92_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id92__主游戏_标志_24(param1:int) : *
      {
         if(this.__id92_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id92_] == undefined || !(int(this.__setPropDict[this.__id92_]) >= 25 && int(this.__setPropDict[this.__id92_]) <= 29)))
         {
            this.__setPropDict[this.__id92_] = param1;
            try
            {
               this.__id92_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id92_.delay = 2;
            this.__id92_.enemyType = 13;
            this.__id92_.interval = 2;
            this.__id92_.isRandom = false;
            this.__id92_.stopPointIdx = 3;
            this.__id92_.totalNum = 2;
            try
            {
               this.__id92_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id93__主游戏_标志_14(param1:int) : *
      {
         if(this.__id93_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id93_] == undefined || !(int(this.__setPropDict[this.__id93_]) >= 15 && int(this.__setPropDict[this.__id93_]) <= 19)))
         {
            this.__setPropDict[this.__id93_] = param1;
            try
            {
               this.__id93_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id93_.delay = 12;
            this.__id93_.enemyType = 8;
            this.__id93_.interval = 2;
            this.__id93_.isRandom = false;
            this.__id93_.stopPointIdx = 3;
            this.__id93_.totalNum = 2;
            try
            {
               this.__id93_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id93__主游戏_标志_19(param1:int) : *
      {
         if(this.__id93_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id93_] == undefined || !(int(this.__setPropDict[this.__id93_]) >= 20 && int(this.__setPropDict[this.__id93_]) <= 24)))
         {
            this.__setPropDict[this.__id93_] = param1;
            try
            {
               this.__id93_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id93_.delay = 10;
            this.__id93_.enemyType = 7;
            this.__id93_.interval = 2;
            this.__id93_.isRandom = false;
            this.__id93_.stopPointIdx = 2;
            this.__id93_.totalNum = 3;
            try
            {
               this.__id93_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id93__主游戏_标志_24(param1:int) : *
      {
         if(this.__id93_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id93_] == undefined || !(int(this.__setPropDict[this.__id93_]) >= 25 && int(this.__setPropDict[this.__id93_]) <= 29)))
         {
            this.__setPropDict[this.__id93_] = param1;
            try
            {
               this.__id93_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id93_.delay = 2;
            this.__id93_.enemyType = 13;
            this.__id93_.interval = 2;
            this.__id93_.isRandom = false;
            this.__id93_.stopPointIdx = 3;
            this.__id93_.totalNum = 2;
            try
            {
               this.__id93_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id94__主游戏_标志_14(param1:int) : *
      {
         if(this.__id94_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id94_] == undefined || !(int(this.__setPropDict[this.__id94_]) >= 15 && int(this.__setPropDict[this.__id94_]) <= 19)))
         {
            this.__setPropDict[this.__id94_] = param1;
            try
            {
               this.__id94_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id94_.betweenRandL = 1150;
            this.__id94_.idx = 2;
            this.__id94_.isBoss = false;
            try
            {
               this.__id94_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id94__主游戏_标志_19(param1:int) : *
      {
         if(this.__id94_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id94_] == undefined || !(int(this.__setPropDict[this.__id94_]) >= 20 && int(this.__setPropDict[this.__id94_]) <= 24)))
         {
            this.__setPropDict[this.__id94_] = param1;
            try
            {
               this.__id94_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id94_.betweenRandL = 1150;
            this.__id94_.idx = 3;
            this.__id94_.isBoss = false;
            try
            {
               this.__id94_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id94__主游戏_标志_24(param1:int) : *
      {
         if(this.__id94_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id94_] == undefined || !(int(this.__setPropDict[this.__id94_]) >= 25 && int(this.__setPropDict[this.__id94_]) <= 29)))
         {
            this.__setPropDict[this.__id94_] = param1;
            try
            {
               this.__id94_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id94_.betweenRandL = 1150;
            this.__id94_.idx = 4;
            this.__id94_.isBoss = false;
            try
            {
               this.__id94_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id95__主游戏_标志_14(param1:int) : *
      {
         if(this.__id95_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id95_] == undefined || !(int(this.__setPropDict[this.__id95_]) >= 15 && int(this.__setPropDict[this.__id95_]) <= 19)))
         {
            this.__setPropDict[this.__id95_] = param1;
            try
            {
               this.__id95_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id95_.delay = 3;
            this.__id95_.enemyType = 8;
            this.__id95_.interval = 2;
            this.__id95_.isRandom = false;
            this.__id95_.stopPointIdx = 2;
            this.__id95_.totalNum = 5;
            try
            {
               this.__id95_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id95__主游戏_标志_19(param1:int) : *
      {
         if(this.__id95_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id95_] == undefined || !(int(this.__setPropDict[this.__id95_]) >= 20 && int(this.__setPropDict[this.__id95_]) <= 24)))
         {
            this.__setPropDict[this.__id95_] = param1;
            try
            {
               this.__id95_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id95_.delay = 2;
            this.__id95_.enemyType = 13;
            this.__id95_.interval = 2;
            this.__id95_.isRandom = false;
            this.__id95_.stopPointIdx = 3;
            this.__id95_.totalNum = 2;
            try
            {
               this.__id95_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id95__主游戏_标志_24(param1:int) : *
      {
         if(this.__id95_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id95_] == undefined || !(int(this.__setPropDict[this.__id95_]) >= 25 && int(this.__setPropDict[this.__id95_]) <= 29)))
         {
            this.__setPropDict[this.__id95_] = param1;
            try
            {
               this.__id95_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id95_.delay = 2;
            this.__id95_.enemyType = 9;
            this.__id95_.interval = 2;
            this.__id95_.isRandom = false;
            this.__id95_.stopPointIdx = 4;
            this.__id95_.totalNum = 1;
            try
            {
               this.__id95_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id96__主游戏_标志_14(param1:int) : *
      {
         if(this.__id96_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id96_] == undefined || !(int(this.__setPropDict[this.__id96_]) >= 15 && int(this.__setPropDict[this.__id96_]) <= 19)))
         {
            this.__setPropDict[this.__id96_] = param1;
            try
            {
               this.__id96_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id96_.delay = 14;
            this.__id96_.enemyType = 8;
            this.__id96_.interval = 2;
            this.__id96_.isRandom = false;
            this.__id96_.stopPointIdx = 3;
            this.__id96_.totalNum = 3;
            try
            {
               this.__id96_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id96__主游戏_标志_19(param1:int) : *
      {
         if(this.__id96_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id96_] == undefined || !(int(this.__setPropDict[this.__id96_]) >= 20 && int(this.__setPropDict[this.__id96_]) <= 24)))
         {
            this.__setPropDict[this.__id96_] = param1;
            try
            {
               this.__id96_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id96_.delay = 2;
            this.__id96_.enemyType = 13;
            this.__id96_.interval = 2;
            this.__id96_.isRandom = false;
            this.__id96_.stopPointIdx = 3;
            this.__id96_.totalNum = 2;
            try
            {
               this.__id96_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id97__主游戏_标志_14(param1:int) : *
      {
         if(this.__id97_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id97_] == undefined || !(int(this.__setPropDict[this.__id97_]) >= 15 && int(this.__setPropDict[this.__id97_]) <= 19)))
         {
            this.__setPropDict[this.__id97_] = param1;
            try
            {
               this.__id97_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id97_.delay = 14;
            this.__id97_.enemyType = 8;
            this.__id97_.interval = 2;
            this.__id97_.isRandom = false;
            this.__id97_.stopPointIdx = 3;
            this.__id97_.totalNum = 3;
            try
            {
               this.__id97_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id97__主游戏_标志_19(param1:int) : *
      {
         if(this.__id97_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id97_] == undefined || !(int(this.__setPropDict[this.__id97_]) >= 20 && int(this.__setPropDict[this.__id97_]) <= 24)))
         {
            this.__setPropDict[this.__id97_] = param1;
            try
            {
               this.__id97_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id97_.delay = 2;
            this.__id97_.enemyType = 13;
            this.__id97_.interval = 2;
            this.__id97_.isRandom = false;
            this.__id97_.stopPointIdx = 3;
            this.__id97_.totalNum = 3;
            try
            {
               this.__id97_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id98__主游戏_标志_14(param1:int) : *
      {
         if(this.__id98_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id98_] == undefined || !(int(this.__setPropDict[this.__id98_]) >= 15 && int(this.__setPropDict[this.__id98_]) <= 19)))
         {
            this.__setPropDict[this.__id98_] = param1;
            try
            {
               this.__id98_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id98_.betweenRandL = 1150;
            this.__id98_.idx = 1;
            this.__id98_.isBoss = false;
            try
            {
               this.__id98_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id98__主游戏_标志_19(param1:int) : *
      {
         if(this.__id98_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id98_] == undefined || !(int(this.__setPropDict[this.__id98_]) >= 20 && int(this.__setPropDict[this.__id98_]) <= 24)))
         {
            this.__setPropDict[this.__id98_] = param1;
            try
            {
               this.__id98_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id98_.betweenRandL = 1150;
            this.__id98_.idx = 4;
            this.__id98_.isBoss = true;
            try
            {
               this.__id98_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id99__主游戏_标志_14(param1:int) : *
      {
         if(this.__id99_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id99_] == undefined || !(int(this.__setPropDict[this.__id99_]) >= 15 && int(this.__setPropDict[this.__id99_]) <= 19)))
         {
            this.__setPropDict[this.__id99_] = param1;
            try
            {
               this.__id99_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id99_.delay = 12;
            this.__id99_.enemyType = 8;
            this.__id99_.interval = 2;
            this.__id99_.isRandom = false;
            this.__id99_.stopPointIdx = 1;
            this.__id99_.totalNum = 3;
            try
            {
               this.__id99_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id99__主游戏_标志_19(param1:int) : *
      {
         if(this.__id99_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id99_] == undefined || !(int(this.__setPropDict[this.__id99_]) >= 20 && int(this.__setPropDict[this.__id99_]) <= 24)))
         {
            this.__setPropDict[this.__id99_] = param1;
            try
            {
               this.__id99_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id99_.delay = 2;
            this.__id99_.enemyType = 11;
            this.__id99_.interval = 2;
            this.__id99_.isRandom = false;
            this.__id99_.stopPointIdx = 4;
            this.__id99_.totalNum = 1;
            try
            {
               this.__id99_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id100__主游戏_标志_14(param1:int) : *
      {
         if(this.__id100_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id100_] == undefined || !(int(this.__setPropDict[this.__id100_]) >= 15 && int(this.__setPropDict[this.__id100_]) <= 19)))
         {
            this.__setPropDict[this.__id100_] = param1;
            try
            {
               this.__id100_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id100_.delay = 12;
            this.__id100_.enemyType = 8;
            this.__id100_.interval = 2;
            this.__id100_.isRandom = false;
            this.__id100_.stopPointIdx = 1;
            this.__id100_.totalNum = 3;
            try
            {
               this.__id100_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id101__主游戏_标志_14(param1:int) : *
      {
         if(this.__id101_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id101_] == undefined || !(int(this.__setPropDict[this.__id101_]) >= 15 && int(this.__setPropDict[this.__id101_]) <= 19)))
         {
            this.__setPropDict[this.__id101_] = param1;
            try
            {
               this.__id101_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id101_.delay = 3;
            this.__id101_.enemyType = 8;
            this.__id101_.interval = 2;
            this.__id101_.isRandom = false;
            this.__id101_.stopPointIdx = 1;
            this.__id101_.totalNum = 4;
            try
            {
               this.__id101_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id102__主游戏_标志_14(param1:int) : *
      {
         if(this.__id102_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id102_] == undefined || !(int(this.__setPropDict[this.__id102_]) >= 15 && int(this.__setPropDict[this.__id102_]) <= 19)))
         {
            this.__setPropDict[this.__id102_] = param1;
            try
            {
               this.__id102_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id102_.betweenRandL = 1150;
            this.__id102_.idx = 0;
            this.__id102_.isBoss = false;
            try
            {
               this.__id102_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id103__主游戏_标志_14(param1:int) : *
      {
         if(this.__id103_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id103_] == undefined || !(int(this.__setPropDict[this.__id103_]) >= 15 && int(this.__setPropDict[this.__id103_]) <= 19)))
         {
            this.__setPropDict[this.__id103_] = param1;
            try
            {
               this.__id103_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id103_.delay = 2;
            this.__id103_.enemyType = 8;
            this.__id103_.interval = 2;
            this.__id103_.isRandom = false;
            this.__id103_.stopPointIdx = 0;
            this.__id103_.totalNum = 3;
            try
            {
               this.__id103_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id104__主游戏_标志_14(param1:int) : *
      {
         if(this.__id104_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id104_] == undefined || !(int(this.__setPropDict[this.__id104_]) >= 15 && int(this.__setPropDict[this.__id104_]) <= 19)))
         {
            this.__setPropDict[this.__id104_] = param1;
            try
            {
               this.__id104_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id104_.delay = 10;
            this.__id104_.enemyType = 8;
            this.__id104_.interval = 2;
            this.__id104_.isRandom = false;
            this.__id104_.stopPointIdx = 0;
            this.__id104_.totalNum = 4;
            try
            {
               this.__id104_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id105__主游戏_标志_14(param1:int) : *
      {
         if(this.__id105_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id105_] == undefined || !(int(this.__setPropDict[this.__id105_]) >= 15 && int(this.__setPropDict[this.__id105_]) <= 19)))
         {
            this.__setPropDict[this.__id105_] = param1;
            try
            {
               this.__id105_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id105_.delay = 4;
            this.__id105_.enemyType = 8;
            this.__id105_.interval = 5;
            this.__id105_.isRandom = false;
            this.__id105_.stopPointIdx = 4;
            this.__id105_.totalNum = 999;
            try
            {
               this.__id105_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id106__主游戏_标志_14(param1:int) : *
      {
         if(this.__id106_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id106_] == undefined || !(int(this.__setPropDict[this.__id106_]) >= 15 && int(this.__setPropDict[this.__id106_]) <= 19)))
         {
            this.__setPropDict[this.__id106_] = param1;
            try
            {
               this.__id106_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id106_.delay = 4;
            this.__id106_.enemyType = 8;
            this.__id106_.interval = 5;
            this.__id106_.isRandom = false;
            this.__id106_.stopPointIdx = 4;
            this.__id106_.totalNum = 999;
            try
            {
               this.__id106_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id107__主游戏_标志_14(param1:int) : *
      {
         if(this.__id107_ != null && param1 >= 15 && param1 <= 19 && (this.__setPropDict[this.__id107_] == undefined || !(int(this.__setPropDict[this.__id107_]) >= 15 && int(this.__setPropDict[this.__id107_]) <= 19)))
         {
            this.__setPropDict[this.__id107_] = param1;
            try
            {
               this.__id107_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id107_.delay = 1;
            this.__id107_.enemyType = 21;
            this.__id107_.interval = 5;
            this.__id107_.isRandom = false;
            this.__id107_.stopPointIdx = 4;
            this.__id107_.totalNum = 1;
            try
            {
               this.__id107_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id108__主游戏_标志_19(param1:int) : *
      {
         if(this.__id108_ != null && param1 >= 20 && param1 <= 44 && (this.__setPropDict[this.__id108_] == undefined || !(int(this.__setPropDict[this.__id108_]) >= 20 && int(this.__setPropDict[this.__id108_]) <= 44)))
         {
            this.__setPropDict[this.__id108_] = param1;
            try
            {
               this.__id108_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id108_.betweenRandL = 1150;
            this.__id108_.idx = 0;
            this.__id108_.isBoss = false;
            try
            {
               this.__id108_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id108__主游戏_标志_44(param1:int) : *
      {
         if(this.__id108_ != null && param1 >= 45 && param1 <= 48 && (this.__setPropDict[this.__id108_] == undefined || !(int(this.__setPropDict[this.__id108_]) >= 45 && int(this.__setPropDict[this.__id108_]) <= 48)))
         {
            this.__setPropDict[this.__id108_] = param1;
            try
            {
               this.__id108_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id108_.betweenRandL = 940;
            this.__id108_.idx = 1;
            this.__id108_.isBoss = false;
            try
            {
               this.__id108_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id109__主游戏_标志_19(param1:int) : *
      {
         if(this.__id109_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id109_] == undefined || !(int(this.__setPropDict[this.__id109_]) >= 20 && int(this.__setPropDict[this.__id109_]) <= 24)))
         {
            this.__setPropDict[this.__id109_] = param1;
            try
            {
               this.__id109_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id109_.delay = 2;
            this.__id109_.enemyType = 8;
            this.__id109_.interval = 2;
            this.__id109_.isRandom = false;
            this.__id109_.stopPointIdx = 0;
            this.__id109_.totalNum = 3;
            try
            {
               this.__id109_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id109__主游戏_标志_24(param1:int) : *
      {
         if(this.__id109_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id109_] == undefined || !(int(this.__setPropDict[this.__id109_]) >= 25 && int(this.__setPropDict[this.__id109_]) <= 29)))
         {
            this.__setPropDict[this.__id109_] = param1;
            try
            {
               this.__id109_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id109_.delay = 12;
            this.__id109_.enemyType = 7;
            this.__id109_.interval = 2;
            this.__id109_.isRandom = false;
            this.__id109_.stopPointIdx = 0;
            this.__id109_.totalNum = 3;
            try
            {
               this.__id109_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id109__主游戏_标志_29(param1:int) : *
      {
         if(this.__id109_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id109_] == undefined || !(int(this.__setPropDict[this.__id109_]) >= 30 && int(this.__setPropDict[this.__id109_]) <= 34)))
         {
            this.__setPropDict[this.__id109_] = param1;
            try
            {
               this.__id109_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id109_.delay = 2;
            this.__id109_.enemyType = 14;
            this.__id109_.interval = 2;
            this.__id109_.isRandom = false;
            this.__id109_.stopPointIdx = 0;
            this.__id109_.totalNum = 2;
            try
            {
               this.__id109_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id109__主游戏_标志_34(param1:int) : *
      {
         if(this.__id109_ != null && param1 >= 35 && param1 <= 44 && (this.__setPropDict[this.__id109_] == undefined || !(int(this.__setPropDict[this.__id109_]) >= 35 && int(this.__setPropDict[this.__id109_]) <= 44)))
         {
            this.__setPropDict[this.__id109_] = param1;
            try
            {
               this.__id109_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id109_.delay = 12;
            this.__id109_.enemyType = 16;
            this.__id109_.interval = 2;
            this.__id109_.isRandom = false;
            this.__id109_.stopPointIdx = 0;
            this.__id109_.totalNum = 5;
            try
            {
               this.__id109_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id109__主游戏_标志_44(param1:int) : *
      {
         if(this.__id109_ != null && param1 >= 45 && param1 <= 48 && (this.__setPropDict[this.__id109_] == undefined || !(int(this.__setPropDict[this.__id109_]) >= 45 && int(this.__setPropDict[this.__id109_]) <= 48)))
         {
            this.__setPropDict[this.__id109_] = param1;
            try
            {
               this.__id109_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id109_.delay = 2;
            this.__id109_.enemyType = 25;
            this.__id109_.interval = 1;
            this.__id109_.isRandom = false;
            this.__id109_.stopPointIdx = 0;
            this.__id109_.totalNum = 10;
            try
            {
               this.__id109_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id110__主游戏_标志_19(param1:int) : *
      {
         if(this.__id110_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id110_] == undefined || !(int(this.__setPropDict[this.__id110_]) >= 20 && int(this.__setPropDict[this.__id110_]) <= 24)))
         {
            this.__setPropDict[this.__id110_] = param1;
            try
            {
               this.__id110_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id110_.delay = 10;
            this.__id110_.enemyType = 7;
            this.__id110_.interval = 2;
            this.__id110_.isRandom = false;
            this.__id110_.stopPointIdx = 0;
            this.__id110_.totalNum = 3;
            try
            {
               this.__id110_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id110__主游戏_标志_24(param1:int) : *
      {
         if(this.__id110_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id110_] == undefined || !(int(this.__setPropDict[this.__id110_]) >= 25 && int(this.__setPropDict[this.__id110_]) <= 29)))
         {
            this.__setPropDict[this.__id110_] = param1;
            try
            {
               this.__id110_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id110_.delay = 12;
            this.__id110_.enemyType = 13;
            this.__id110_.interval = 2;
            this.__id110_.isRandom = false;
            this.__id110_.stopPointIdx = 0;
            this.__id110_.totalNum = 3;
            try
            {
               this.__id110_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id110__主游戏_标志_29(param1:int) : *
      {
         if(this.__id110_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id110_] == undefined || !(int(this.__setPropDict[this.__id110_]) >= 30 && int(this.__setPropDict[this.__id110_]) <= 34)))
         {
            this.__setPropDict[this.__id110_] = param1;
            try
            {
               this.__id110_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id110_.delay = 8;
            this.__id110_.enemyType = 14;
            this.__id110_.interval = 2;
            this.__id110_.isRandom = false;
            this.__id110_.stopPointIdx = 0;
            this.__id110_.totalNum = 5;
            try
            {
               this.__id110_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id110__主游戏_标志_34(param1:int) : *
      {
         if(this.__id110_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id110_] == undefined || !(int(this.__setPropDict[this.__id110_]) >= 35 && int(this.__setPropDict[this.__id110_]) <= 39)))
         {
            this.__setPropDict[this.__id110_] = param1;
            try
            {
               this.__id110_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id110_.delay = 2;
            this.__id110_.enemyType = 20;
            this.__id110_.interval = 2;
            this.__id110_.isRandom = false;
            this.__id110_.stopPointIdx = 0;
            this.__id110_.totalNum = 1;
            try
            {
               this.__id110_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id110__主游戏_标志_39(param1:int) : *
      {
         if(this.__id110_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id110_] == undefined || !(int(this.__setPropDict[this.__id110_]) >= 40 && int(this.__setPropDict[this.__id110_]) <= 44)))
         {
            this.__setPropDict[this.__id110_] = param1;
            try
            {
               this.__id110_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id110_.delay = 12;
            this.__id110_.enemyType = 14;
            this.__id110_.interval = 2;
            this.__id110_.isRandom = false;
            this.__id110_.stopPointIdx = 0;
            this.__id110_.totalNum = 5;
            try
            {
               this.__id110_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id110__主游戏_标志_44(param1:int) : *
      {
         if(this.__id110_ != null && param1 >= 45 && param1 <= 48 && (this.__setPropDict[this.__id110_] == undefined || !(int(this.__setPropDict[this.__id110_]) >= 45 && int(this.__setPropDict[this.__id110_]) <= 48)))
         {
            this.__setPropDict[this.__id110_] = param1;
            try
            {
               this.__id110_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id110_.delay = 2;
            this.__id110_.enemyType = 25;
            this.__id110_.interval = 2;
            this.__id110_.isRandom = false;
            this.__id110_.stopPointIdx = 0;
            this.__id110_.totalNum = 20;
            try
            {
               this.__id110_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id111__主游戏_标志_19(param1:int) : *
      {
         if(this.__id111_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id111_] == undefined || !(int(this.__setPropDict[this.__id111_]) >= 20 && int(this.__setPropDict[this.__id111_]) <= 24)))
         {
            this.__setPropDict[this.__id111_] = param1;
            try
            {
               this.__id111_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id111_.delay = 10;
            this.__id111_.enemyType = 7;
            this.__id111_.interval = 2;
            this.__id111_.isRandom = false;
            this.__id111_.stopPointIdx = 0;
            this.__id111_.totalNum = 3;
            try
            {
               this.__id111_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id111__主游戏_标志_24(param1:int) : *
      {
         if(this.__id111_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id111_] == undefined || !(int(this.__setPropDict[this.__id111_]) >= 25 && int(this.__setPropDict[this.__id111_]) <= 29)))
         {
            this.__setPropDict[this.__id111_] = param1;
            try
            {
               this.__id111_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id111_.delay = 12;
            this.__id111_.enemyType = 7;
            this.__id111_.interval = 2;
            this.__id111_.isRandom = false;
            this.__id111_.stopPointIdx = 1;
            this.__id111_.totalNum = 3;
            try
            {
               this.__id111_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id111__主游戏_标志_29(param1:int) : *
      {
         if(this.__id111_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id111_] == undefined || !(int(this.__setPropDict[this.__id111_]) >= 30 && int(this.__setPropDict[this.__id111_]) <= 34)))
         {
            this.__setPropDict[this.__id111_] = param1;
            try
            {
               this.__id111_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id111_.delay = 12;
            this.__id111_.enemyType = 16;
            this.__id111_.interval = 2;
            this.__id111_.isRandom = false;
            this.__id111_.stopPointIdx = 1;
            this.__id111_.totalNum = 5;
            try
            {
               this.__id111_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id111__主游戏_标志_34(param1:int) : *
      {
         if(this.__id111_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id111_] == undefined || !(int(this.__setPropDict[this.__id111_]) >= 35 && int(this.__setPropDict[this.__id111_]) <= 39)))
         {
            this.__setPropDict[this.__id111_] = param1;
            try
            {
               this.__id111_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id111_.delay = 2;
            this.__id111_.enemyType = 20;
            this.__id111_.interval = 2;
            this.__id111_.isRandom = false;
            this.__id111_.stopPointIdx = 1;
            this.__id111_.totalNum = 1;
            try
            {
               this.__id111_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id111__主游戏_标志_39(param1:int) : *
      {
         if(this.__id111_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id111_] == undefined || !(int(this.__setPropDict[this.__id111_]) >= 40 && int(this.__setPropDict[this.__id111_]) <= 44)))
         {
            this.__setPropDict[this.__id111_] = param1;
            try
            {
               this.__id111_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id111_.delay = 2;
            this.__id111_.enemyType = 14;
            this.__id111_.interval = 2;
            this.__id111_.isRandom = false;
            this.__id111_.stopPointIdx = 1;
            this.__id111_.totalNum = 5;
            try
            {
               this.__id111_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id112__主游戏_标志_19(param1:int) : *
      {
         if(this.__id112_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id112_] == undefined || !(int(this.__setPropDict[this.__id112_]) >= 20 && int(this.__setPropDict[this.__id112_]) <= 24)))
         {
            this.__setPropDict[this.__id112_] = param1;
            try
            {
               this.__id112_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id112_.delay = 2;
            this.__id112_.enemyType = 7;
            this.__id112_.interval = 2;
            this.__id112_.isRandom = false;
            this.__id112_.stopPointIdx = 1;
            this.__id112_.totalNum = 2;
            try
            {
               this.__id112_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id112__主游戏_标志_24(param1:int) : *
      {
         if(this.__id112_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id112_] == undefined || !(int(this.__setPropDict[this.__id112_]) >= 25 && int(this.__setPropDict[this.__id112_]) <= 29)))
         {
            this.__setPropDict[this.__id112_] = param1;
            try
            {
               this.__id112_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id112_.delay = 2;
            this.__id112_.enemyType = 13;
            this.__id112_.interval = 2;
            this.__id112_.isRandom = false;
            this.__id112_.stopPointIdx = 2;
            this.__id112_.totalNum = 1;
            try
            {
               this.__id112_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id112__主游戏_标志_29(param1:int) : *
      {
         if(this.__id112_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id112_] == undefined || !(int(this.__setPropDict[this.__id112_]) >= 30 && int(this.__setPropDict[this.__id112_]) <= 34)))
         {
            this.__setPropDict[this.__id112_] = param1;
            try
            {
               this.__id112_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id112_.delay = 12;
            this.__id112_.enemyType = 16;
            this.__id112_.interval = 2;
            this.__id112_.isRandom = false;
            this.__id112_.stopPointIdx = 3;
            this.__id112_.totalNum = 5;
            try
            {
               this.__id112_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id112__主游戏_标志_34(param1:int) : *
      {
         if(this.__id112_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id112_] == undefined || !(int(this.__setPropDict[this.__id112_]) >= 35 && int(this.__setPropDict[this.__id112_]) <= 39)))
         {
            this.__setPropDict[this.__id112_] = param1;
            try
            {
               this.__id112_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id112_.delay = 2;
            this.__id112_.enemyType = 20;
            this.__id112_.interval = 2;
            this.__id112_.isRandom = false;
            this.__id112_.stopPointIdx = 2;
            this.__id112_.totalNum = 1;
            try
            {
               this.__id112_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id112__主游戏_标志_39(param1:int) : *
      {
         if(this.__id112_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id112_] == undefined || !(int(this.__setPropDict[this.__id112_]) >= 40 && int(this.__setPropDict[this.__id112_]) <= 44)))
         {
            this.__setPropDict[this.__id112_] = param1;
            try
            {
               this.__id112_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id112_.delay = 2;
            this.__id112_.enemyType = 14;
            this.__id112_.interval = 2;
            this.__id112_.isRandom = false;
            this.__id112_.stopPointIdx = 3;
            this.__id112_.totalNum = 5;
            try
            {
               this.__id112_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id113__主游戏_标志_19(param1:int) : *
      {
         if(this.__id113_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id113_] == undefined || !(int(this.__setPropDict[this.__id113_]) >= 20 && int(this.__setPropDict[this.__id113_]) <= 24)))
         {
            this.__setPropDict[this.__id113_] = param1;
            try
            {
               this.__id113_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id113_.delay = 10;
            this.__id113_.enemyType = 8;
            this.__id113_.interval = 2;
            this.__id113_.isRandom = false;
            this.__id113_.stopPointIdx = 1;
            this.__id113_.totalNum = 3;
            try
            {
               this.__id113_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id113__主游戏_标志_24(param1:int) : *
      {
         if(this.__id113_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id113_] == undefined || !(int(this.__setPropDict[this.__id113_]) >= 25 && int(this.__setPropDict[this.__id113_]) <= 29)))
         {
            this.__setPropDict[this.__id113_] = param1;
            try
            {
               this.__id113_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id113_.delay = 2;
            this.__id113_.enemyType = 13;
            this.__id113_.interval = 2;
            this.__id113_.isRandom = false;
            this.__id113_.stopPointIdx = 2;
            this.__id113_.totalNum = 1;
            try
            {
               this.__id113_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id113__主游戏_标志_29(param1:int) : *
      {
         if(this.__id113_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id113_] == undefined || !(int(this.__setPropDict[this.__id113_]) >= 30 && int(this.__setPropDict[this.__id113_]) <= 34)))
         {
            this.__setPropDict[this.__id113_] = param1;
            try
            {
               this.__id113_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id113_.delay = 12;
            this.__id113_.enemyType = 14;
            this.__id113_.interval = 2;
            this.__id113_.isRandom = false;
            this.__id113_.stopPointIdx = 3;
            this.__id113_.totalNum = 5;
            try
            {
               this.__id113_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id113__主游戏_标志_34(param1:int) : *
      {
         if(this.__id113_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id113_] == undefined || !(int(this.__setPropDict[this.__id113_]) >= 35 && int(this.__setPropDict[this.__id113_]) <= 39)))
         {
            this.__setPropDict[this.__id113_] = param1;
            try
            {
               this.__id113_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id113_.delay = 2;
            this.__id113_.enemyType = 14;
            this.__id113_.interval = 2;
            this.__id113_.isRandom = false;
            this.__id113_.stopPointIdx = 2;
            this.__id113_.totalNum = 3;
            try
            {
               this.__id113_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id113__主游戏_标志_39(param1:int) : *
      {
         if(this.__id113_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id113_] == undefined || !(int(this.__setPropDict[this.__id113_]) >= 40 && int(this.__setPropDict[this.__id113_]) <= 44)))
         {
            this.__setPropDict[this.__id113_] = param1;
            try
            {
               this.__id113_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id113_.delay = 2;
            this.__id113_.enemyType = 16;
            this.__id113_.interval = 2;
            this.__id113_.isRandom = false;
            this.__id113_.stopPointIdx = 3;
            this.__id113_.totalNum = 5;
            try
            {
               this.__id113_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id114__主游戏_标志_19(param1:int) : *
      {
         if(this.__id114_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id114_] == undefined || !(int(this.__setPropDict[this.__id114_]) >= 20 && int(this.__setPropDict[this.__id114_]) <= 24)))
         {
            this.__setPropDict[this.__id114_] = param1;
            try
            {
               this.__id114_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id114_.delay = 10;
            this.__id114_.enemyType = 7;
            this.__id114_.interval = 2;
            this.__id114_.isRandom = false;
            this.__id114_.stopPointIdx = 1;
            this.__id114_.totalNum = 3;
            try
            {
               this.__id114_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id114__主游戏_标志_24(param1:int) : *
      {
         if(this.__id114_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id114_] == undefined || !(int(this.__setPropDict[this.__id114_]) >= 25 && int(this.__setPropDict[this.__id114_]) <= 29)))
         {
            this.__setPropDict[this.__id114_] = param1;
            try
            {
               this.__id114_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id114_.delay = 2;
            this.__id114_.enemyType = 13;
            this.__id114_.interval = 2;
            this.__id114_.isRandom = false;
            this.__id114_.stopPointIdx = 2;
            this.__id114_.totalNum = 1;
            try
            {
               this.__id114_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id114__主游戏_标志_29(param1:int) : *
      {
         if(this.__id114_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id114_] == undefined || !(int(this.__setPropDict[this.__id114_]) >= 30 && int(this.__setPropDict[this.__id114_]) <= 34)))
         {
            this.__setPropDict[this.__id114_] = param1;
            try
            {
               this.__id114_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id114_.delay = 2;
            this.__id114_.enemyType = 17;
            this.__id114_.interval = 2;
            this.__id114_.isRandom = false;
            this.__id114_.stopPointIdx = 4;
            this.__id114_.totalNum = 1;
            try
            {
               this.__id114_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id114__主游戏_标志_34(param1:int) : *
      {
         if(this.__id114_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id114_] == undefined || !(int(this.__setPropDict[this.__id114_]) >= 35 && int(this.__setPropDict[this.__id114_]) <= 39)))
         {
            this.__setPropDict[this.__id114_] = param1;
            try
            {
               this.__id114_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id114_.delay = 12;
            this.__id114_.enemyType = 16;
            this.__id114_.interval = 2;
            this.__id114_.isRandom = false;
            this.__id114_.stopPointIdx = 2;
            this.__id114_.totalNum = 5;
            try
            {
               this.__id114_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id114__主游戏_标志_39(param1:int) : *
      {
         if(this.__id114_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id114_] == undefined || !(int(this.__setPropDict[this.__id114_]) >= 40 && int(this.__setPropDict[this.__id114_]) <= 44)))
         {
            this.__setPropDict[this.__id114_] = param1;
            try
            {
               this.__id114_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id114_.delay = 12;
            this.__id114_.enemyType = 17;
            this.__id114_.interval = 2;
            this.__id114_.isRandom = false;
            this.__id114_.stopPointIdx = 3;
            this.__id114_.totalNum = 5;
            try
            {
               this.__id114_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id115__主游戏_标志_19(param1:int) : *
      {
         if(this.__id115_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id115_] == undefined || !(int(this.__setPropDict[this.__id115_]) >= 20 && int(this.__setPropDict[this.__id115_]) <= 24)))
         {
            this.__setPropDict[this.__id115_] = param1;
            try
            {
               this.__id115_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id115_.delay = 10;
            this.__id115_.enemyType = 13;
            this.__id115_.interval = 2;
            this.__id115_.isRandom = false;
            this.__id115_.stopPointIdx = 1;
            this.__id115_.totalNum = 2;
            try
            {
               this.__id115_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id115__主游戏_标志_24(param1:int) : *
      {
         if(this.__id115_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id115_] == undefined || !(int(this.__setPropDict[this.__id115_]) >= 25 && int(this.__setPropDict[this.__id115_]) <= 29)))
         {
            this.__setPropDict[this.__id115_] = param1;
            try
            {
               this.__id115_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id115_.delay = 12;
            this.__id115_.enemyType = 13;
            this.__id115_.interval = 2;
            this.__id115_.isRandom = false;
            this.__id115_.stopPointIdx = 2;
            this.__id115_.totalNum = 3;
            try
            {
               this.__id115_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id115__主游戏_标志_29(param1:int) : *
      {
         if(this.__id115_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id115_] == undefined || !(int(this.__setPropDict[this.__id115_]) >= 30 && int(this.__setPropDict[this.__id115_]) <= 34)))
         {
            this.__setPropDict[this.__id115_] = param1;
            try
            {
               this.__id115_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id115_.delay = 2;
            this.__id115_.enemyType = 14;
            this.__id115_.interval = 2;
            this.__id115_.isRandom = false;
            this.__id115_.stopPointIdx = 4;
            this.__id115_.totalNum = 3;
            try
            {
               this.__id115_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id115__主游戏_标志_34(param1:int) : *
      {
         if(this.__id115_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id115_] == undefined || !(int(this.__setPropDict[this.__id115_]) >= 35 && int(this.__setPropDict[this.__id115_]) <= 39)))
         {
            this.__setPropDict[this.__id115_] = param1;
            try
            {
               this.__id115_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id115_.delay = 12;
            this.__id115_.enemyType = 14;
            this.__id115_.interval = 2;
            this.__id115_.isRandom = false;
            this.__id115_.stopPointIdx = 2;
            this.__id115_.totalNum = 5;
            try
            {
               this.__id115_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id115__主游戏_标志_39(param1:int) : *
      {
         if(this.__id115_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id115_] == undefined || !(int(this.__setPropDict[this.__id115_]) >= 40 && int(this.__setPropDict[this.__id115_]) <= 44)))
         {
            this.__setPropDict[this.__id115_] = param1;
            try
            {
               this.__id115_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id115_.delay = 12;
            this.__id115_.enemyType = 20;
            this.__id115_.interval = 2;
            this.__id115_.isRandom = false;
            this.__id115_.stopPointIdx = 3;
            this.__id115_.totalNum = 5;
            try
            {
               this.__id115_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id116__主游戏_标志_19(param1:int) : *
      {
         if(this.__id116_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id116_] == undefined || !(int(this.__setPropDict[this.__id116_]) >= 20 && int(this.__setPropDict[this.__id116_]) <= 24)))
         {
            this.__setPropDict[this.__id116_] = param1;
            try
            {
               this.__id116_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id116_.delay = 10;
            this.__id116_.enemyType = 13;
            this.__id116_.interval = 2;
            this.__id116_.isRandom = false;
            this.__id116_.stopPointIdx = 3;
            this.__id116_.totalNum = 2;
            try
            {
               this.__id116_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id116__主游戏_标志_24(param1:int) : *
      {
         if(this.__id116_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id116_] == undefined || !(int(this.__setPropDict[this.__id116_]) >= 25 && int(this.__setPropDict[this.__id116_]) <= 29)))
         {
            this.__setPropDict[this.__id116_] = param1;
            try
            {
               this.__id116_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id116_.delay = 12;
            this.__id116_.enemyType = 13;
            this.__id116_.interval = 2;
            this.__id116_.isRandom = false;
            this.__id116_.stopPointIdx = 3;
            this.__id116_.totalNum = 3;
            try
            {
               this.__id116_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id117__主游戏_标志_19(param1:int) : *
      {
         if(this.__id117_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id117_] == undefined || !(int(this.__setPropDict[this.__id117_]) >= 20 && int(this.__setPropDict[this.__id117_]) <= 24)))
         {
            this.__setPropDict[this.__id117_] = param1;
            try
            {
               this.__id117_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id117_.delay = 15;
            this.__id117_.enemyType = 7;
            this.__id117_.interval = 2;
            this.__id117_.isRandom = false;
            this.__id117_.stopPointIdx = 3;
            this.__id117_.totalNum = 4;
            try
            {
               this.__id117_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id118__主游戏_标志_19(param1:int) : *
      {
         if(this.__id118_ != null && param1 >= 20 && param1 <= 24 && (this.__setPropDict[this.__id118_] == undefined || !(int(this.__setPropDict[this.__id118_]) >= 20 && int(this.__setPropDict[this.__id118_]) <= 24)))
         {
            this.__setPropDict[this.__id118_] = param1;
            try
            {
               this.__id118_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id118_.delay = 15;
            this.__id118_.enemyType = 7;
            this.__id118_.interval = 2;
            this.__id118_.isRandom = false;
            this.__id118_.stopPointIdx = 3;
            this.__id118_.totalNum = 4;
            try
            {
               this.__id118_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id123__主游戏_标志_24(param1:int) : *
      {
         if(this.__id123_ != null && param1 >= 25 && param1 <= 44 && (this.__setPropDict[this.__id123_] == undefined || !(int(this.__setPropDict[this.__id123_]) >= 25 && int(this.__setPropDict[this.__id123_]) <= 44)))
         {
            this.__setPropDict[this.__id123_] = param1;
            try
            {
               this.__id123_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id123_.betweenRandL = 1150;
            this.__id123_.idx = 1;
            this.__id123_.isBoss = false;
            try
            {
               this.__id123_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id124__主游戏_标志_24(param1:int) : *
      {
         if(this.__id124_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id124_] == undefined || !(int(this.__setPropDict[this.__id124_]) >= 25 && int(this.__setPropDict[this.__id124_]) <= 29)))
         {
            this.__setPropDict[this.__id124_] = param1;
            try
            {
               this.__id124_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id124_.delay = 12;
            this.__id124_.enemyType = 15;
            this.__id124_.interval = 2;
            this.__id124_.isRandom = false;
            this.__id124_.stopPointIdx = 1;
            this.__id124_.totalNum = 5;
            try
            {
               this.__id124_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id124__主游戏_标志_29(param1:int) : *
      {
         if(this.__id124_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id124_] == undefined || !(int(this.__setPropDict[this.__id124_]) >= 30 && int(this.__setPropDict[this.__id124_]) <= 34)))
         {
            this.__setPropDict[this.__id124_] = param1;
            try
            {
               this.__id124_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id124_.delay = 12;
            this.__id124_.enemyType = 16;
            this.__id124_.interval = 2;
            this.__id124_.isRandom = false;
            this.__id124_.stopPointIdx = 1;
            this.__id124_.totalNum = 5;
            try
            {
               this.__id124_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id124__主游戏_标志_34(param1:int) : *
      {
         if(this.__id124_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id124_] == undefined || !(int(this.__setPropDict[this.__id124_]) >= 35 && int(this.__setPropDict[this.__id124_]) <= 39)))
         {
            this.__setPropDict[this.__id124_] = param1;
            try
            {
               this.__id124_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id124_.delay = 2;
            this.__id124_.enemyType = 20;
            this.__id124_.interval = 2;
            this.__id124_.isRandom = false;
            this.__id124_.stopPointIdx = 1;
            this.__id124_.totalNum = 1;
            try
            {
               this.__id124_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id124__主游戏_标志_39(param1:int) : *
      {
         if(this.__id124_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id124_] == undefined || !(int(this.__setPropDict[this.__id124_]) >= 40 && int(this.__setPropDict[this.__id124_]) <= 44)))
         {
            this.__setPropDict[this.__id124_] = param1;
            try
            {
               this.__id124_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id124_.delay = 2;
            this.__id124_.enemyType = 16;
            this.__id124_.interval = 2;
            this.__id124_.isRandom = false;
            this.__id124_.stopPointIdx = 1;
            this.__id124_.totalNum = 5;
            try
            {
               this.__id124_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id125__主游戏_标志_24(param1:int) : *
      {
         if(this.__id125_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id125_] == undefined || !(int(this.__setPropDict[this.__id125_]) >= 25 && int(this.__setPropDict[this.__id125_]) <= 29)))
         {
            this.__setPropDict[this.__id125_] = param1;
            try
            {
               this.__id125_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id125_.delay = 2;
            this.__id125_.enemyType = 15;
            this.__id125_.interval = 2;
            this.__id125_.isRandom = false;
            this.__id125_.stopPointIdx = 1;
            this.__id125_.totalNum = 3;
            try
            {
               this.__id125_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id125__主游戏_标志_29(param1:int) : *
      {
         if(this.__id125_ != null && param1 >= 30 && param1 <= 39 && (this.__setPropDict[this.__id125_] == undefined || !(int(this.__setPropDict[this.__id125_]) >= 30 && int(this.__setPropDict[this.__id125_]) <= 39)))
         {
            this.__setPropDict[this.__id125_] = param1;
            try
            {
               this.__id125_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id125_.delay = 2;
            this.__id125_.enemyType = 14;
            this.__id125_.interval = 2;
            this.__id125_.isRandom = false;
            this.__id125_.stopPointIdx = 1;
            this.__id125_.totalNum = 3;
            try
            {
               this.__id125_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id125__主游戏_标志_39(param1:int) : *
      {
         if(this.__id125_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id125_] == undefined || !(int(this.__setPropDict[this.__id125_]) >= 40 && int(this.__setPropDict[this.__id125_]) <= 44)))
         {
            this.__setPropDict[this.__id125_] = param1;
            try
            {
               this.__id125_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id125_.delay = 12;
            this.__id125_.enemyType = 17;
            this.__id125_.interval = 2;
            this.__id125_.isRandom = false;
            this.__id125_.stopPointIdx = 1;
            this.__id125_.totalNum = 5;
            try
            {
               this.__id125_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id126__主游戏_标志_24(param1:int) : *
      {
         if(this.__id126_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id126_] == undefined || !(int(this.__setPropDict[this.__id126_]) >= 25 && int(this.__setPropDict[this.__id126_]) <= 29)))
         {
            this.__setPropDict[this.__id126_] = param1;
            try
            {
               this.__id126_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id126_.delay = 12;
            this.__id126_.enemyType = 13;
            this.__id126_.interval = 2;
            this.__id126_.isRandom = false;
            this.__id126_.stopPointIdx = 1;
            this.__id126_.totalNum = 3;
            try
            {
               this.__id126_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id126__主游戏_标志_29(param1:int) : *
      {
         if(this.__id126_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id126_] == undefined || !(int(this.__setPropDict[this.__id126_]) >= 30 && int(this.__setPropDict[this.__id126_]) <= 34)))
         {
            this.__setPropDict[this.__id126_] = param1;
            try
            {
               this.__id126_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id126_.delay = 2;
            this.__id126_.enemyType = 14;
            this.__id126_.interval = 2;
            this.__id126_.isRandom = false;
            this.__id126_.stopPointIdx = 2;
            this.__id126_.totalNum = 5;
            try
            {
               this.__id126_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id126__主游戏_标志_34(param1:int) : *
      {
         if(this.__id126_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id126_] == undefined || !(int(this.__setPropDict[this.__id126_]) >= 35 && int(this.__setPropDict[this.__id126_]) <= 39)))
         {
            this.__setPropDict[this.__id126_] = param1;
            try
            {
               this.__id126_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id126_.delay = 2;
            this.__id126_.enemyType = 10;
            this.__id126_.interval = 2;
            this.__id126_.isRandom = false;
            this.__id126_.stopPointIdx = 4;
            this.__id126_.totalNum = 1;
            try
            {
               this.__id126_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id126__主游戏_标志_39(param1:int) : *
      {
         if(this.__id126_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id126_] == undefined || !(int(this.__setPropDict[this.__id126_]) >= 40 && int(this.__setPropDict[this.__id126_]) <= 44)))
         {
            this.__setPropDict[this.__id126_] = param1;
            try
            {
               this.__id126_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id126_.delay = 2;
            this.__id126_.enemyType = 14;
            this.__id126_.interval = 2;
            this.__id126_.isRandom = false;
            this.__id126_.stopPointIdx = 2;
            this.__id126_.totalNum = 5;
            try
            {
               this.__id126_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id127__主游戏_标志_24(param1:int) : *
      {
         if(this.__id127_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id127_] == undefined || !(int(this.__setPropDict[this.__id127_]) >= 25 && int(this.__setPropDict[this.__id127_]) <= 29)))
         {
            this.__setPropDict[this.__id127_] = param1;
            try
            {
               this.__id127_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id127_.delay = 2;
            this.__id127_.enemyType = 13;
            this.__id127_.interval = 2;
            this.__id127_.isRandom = false;
            this.__id127_.stopPointIdx = 1;
            this.__id127_.totalNum = 2;
            try
            {
               this.__id127_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id127__主游戏_标志_29(param1:int) : *
      {
         if(this.__id127_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id127_] == undefined || !(int(this.__setPropDict[this.__id127_]) >= 30 && int(this.__setPropDict[this.__id127_]) <= 34)))
         {
            this.__setPropDict[this.__id127_] = param1;
            try
            {
               this.__id127_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id127_.delay = 12;
            this.__id127_.enemyType = 16;
            this.__id127_.interval = 2;
            this.__id127_.isRandom = false;
            this.__id127_.stopPointIdx = 2;
            this.__id127_.totalNum = 5;
            try
            {
               this.__id127_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id127__主游戏_标志_34(param1:int) : *
      {
         if(this.__id127_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id127_] == undefined || !(int(this.__setPropDict[this.__id127_]) >= 35 && int(this.__setPropDict[this.__id127_]) <= 39)))
         {
            this.__setPropDict[this.__id127_] = param1;
            try
            {
               this.__id127_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id127_.delay = 2;
            this.__id127_.enemyType = 20;
            this.__id127_.interval = 2;
            this.__id127_.isRandom = false;
            this.__id127_.stopPointIdx = 0;
            this.__id127_.totalNum = 1;
            try
            {
               this.__id127_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id127__主游戏_标志_39(param1:int) : *
      {
         if(this.__id127_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id127_] == undefined || !(int(this.__setPropDict[this.__id127_]) >= 40 && int(this.__setPropDict[this.__id127_]) <= 44)))
         {
            this.__setPropDict[this.__id127_] = param1;
            try
            {
               this.__id127_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id127_.delay = 2;
            this.__id127_.enemyType = 16;
            this.__id127_.interval = 2;
            this.__id127_.isRandom = false;
            this.__id127_.stopPointIdx = 2;
            this.__id127_.totalNum = 5;
            try
            {
               this.__id127_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id128__主游戏_标志_24(param1:int) : *
      {
         if(this.__id128_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id128_] == undefined || !(int(this.__setPropDict[this.__id128_]) >= 25 && int(this.__setPropDict[this.__id128_]) <= 29)))
         {
            this.__setPropDict[this.__id128_] = param1;
            try
            {
               this.__id128_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id128_.delay = 12;
            this.__id128_.enemyType = 7;
            this.__id128_.interval = 2;
            this.__id128_.isRandom = false;
            this.__id128_.stopPointIdx = 2;
            this.__id128_.totalNum = 3;
            try
            {
               this.__id128_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id128__主游戏_标志_29(param1:int) : *
      {
         if(this.__id128_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id128_] == undefined || !(int(this.__setPropDict[this.__id128_]) >= 30 && int(this.__setPropDict[this.__id128_]) <= 34)))
         {
            this.__setPropDict[this.__id128_] = param1;
            try
            {
               this.__id128_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id128_.delay = 6;
            this.__id128_.enemyType = 16;
            this.__id128_.interval = 2;
            this.__id128_.isRandom = false;
            this.__id128_.stopPointIdx = 4;
            this.__id128_.totalNum = 5;
            try
            {
               this.__id128_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id128__主游戏_标志_34(param1:int) : *
      {
         if(this.__id128_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id128_] == undefined || !(int(this.__setPropDict[this.__id128_]) >= 35 && int(this.__setPropDict[this.__id128_]) <= 39)))
         {
            this.__setPropDict[this.__id128_] = param1;
            try
            {
               this.__id128_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id128_.delay = 2;
            this.__id128_.enemyType = 16;
            this.__id128_.interval = 2;
            this.__id128_.isRandom = false;
            this.__id128_.stopPointIdx = 2;
            this.__id128_.totalNum = 3;
            try
            {
               this.__id128_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id129__主游戏_标志_24(param1:int) : *
      {
         if(this.__id129_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id129_] == undefined || !(int(this.__setPropDict[this.__id129_]) >= 25 && int(this.__setPropDict[this.__id129_]) <= 29)))
         {
            this.__setPropDict[this.__id129_] = param1;
            try
            {
               this.__id129_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id129_.delay = 12;
            this.__id129_.enemyType = 7;
            this.__id129_.interval = 2;
            this.__id129_.isRandom = false;
            this.__id129_.stopPointIdx = 2;
            this.__id129_.totalNum = 3;
            try
            {
               this.__id129_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id129__主游戏_标志_29(param1:int) : *
      {
         if(this.__id129_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id129_] == undefined || !(int(this.__setPropDict[this.__id129_]) >= 30 && int(this.__setPropDict[this.__id129_]) <= 34)))
         {
            this.__setPropDict[this.__id129_] = param1;
            try
            {
               this.__id129_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id129_.delay = 6;
            this.__id129_.enemyType = 14;
            this.__id129_.interval = 2;
            this.__id129_.isRandom = false;
            this.__id129_.stopPointIdx = 4;
            this.__id129_.totalNum = 5;
            try
            {
               this.__id129_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id129__主游戏_标志_34(param1:int) : *
      {
         if(this.__id129_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id129_] == undefined || !(int(this.__setPropDict[this.__id129_]) >= 35 && int(this.__setPropDict[this.__id129_]) <= 39)))
         {
            this.__setPropDict[this.__id129_] = param1;
            try
            {
               this.__id129_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id129_.delay = 2;
            this.__id129_.enemyType = 20;
            this.__id129_.interval = 2;
            this.__id129_.isRandom = false;
            this.__id129_.stopPointIdx = 3;
            this.__id129_.totalNum = 2;
            try
            {
               this.__id129_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id130__主游戏_标志_24(param1:int) : *
      {
         if(this.__id130_ != null && param1 >= 25 && param1 <= 29 && (this.__setPropDict[this.__id130_] == undefined || !(int(this.__setPropDict[this.__id130_]) >= 25 && int(this.__setPropDict[this.__id130_]) <= 29)))
         {
            this.__setPropDict[this.__id130_] = param1;
            try
            {
               this.__id130_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id130_.delay = 12;
            this.__id130_.enemyType = 7;
            this.__id130_.interval = 2;
            this.__id130_.isRandom = false;
            this.__id130_.stopPointIdx = 3;
            this.__id130_.totalNum = 3;
            try
            {
               this.__id130_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id131__主游戏_标志_29(param1:int) : *
      {
         if(this.__id131_ != null && param1 >= 30 && param1 <= 39 && (this.__setPropDict[this.__id131_] == undefined || !(int(this.__setPropDict[this.__id131_]) >= 30 && int(this.__setPropDict[this.__id131_]) <= 39)))
         {
            this.__setPropDict[this.__id131_] = param1;
            try
            {
               this.__id131_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id131_.betweenRandL = 1150;
            this.__id131_.idx = 2;
            this.__id131_.isBoss = false;
            try
            {
               this.__id131_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id132__主游戏_标志_29(param1:int) : *
      {
         if(this.__id132_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id132_] == undefined || !(int(this.__setPropDict[this.__id132_]) >= 30 && int(this.__setPropDict[this.__id132_]) <= 34)))
         {
            this.__setPropDict[this.__id132_] = param1;
            try
            {
               this.__id132_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id132_.delay = 12;
            this.__id132_.enemyType = 14;
            this.__id132_.interval = 2;
            this.__id132_.isRandom = false;
            this.__id132_.stopPointIdx = 2;
            this.__id132_.totalNum = 5;
            try
            {
               this.__id132_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id132__主游戏_标志_34(param1:int) : *
      {
         if(this.__id132_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id132_] == undefined || !(int(this.__setPropDict[this.__id132_]) >= 35 && int(this.__setPropDict[this.__id132_]) <= 39)))
         {
            this.__setPropDict[this.__id132_] = param1;
            try
            {
               this.__id132_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id132_.delay = 2;
            this.__id132_.enemyType = 16;
            this.__id132_.interval = 2;
            this.__id132_.isRandom = false;
            this.__id132_.stopPointIdx = 1;
            this.__id132_.totalNum = 3;
            try
            {
               this.__id132_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id132__主游戏_标志_39(param1:int) : *
      {
         if(this.__id132_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id132_] == undefined || !(int(this.__setPropDict[this.__id132_]) >= 40 && int(this.__setPropDict[this.__id132_]) <= 44)))
         {
            this.__setPropDict[this.__id132_] = param1;
            try
            {
               this.__id132_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id132_.delay = 12;
            this.__id132_.enemyType = 17;
            this.__id132_.interval = 2;
            this.__id132_.isRandom = false;
            this.__id132_.stopPointIdx = 2;
            this.__id132_.totalNum = 5;
            try
            {
               this.__id132_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id133__主游戏_标志_29(param1:int) : *
      {
         if(this.__id133_ != null && param1 >= 30 && param1 <= 34 && (this.__setPropDict[this.__id133_] == undefined || !(int(this.__setPropDict[this.__id133_]) >= 30 && int(this.__setPropDict[this.__id133_]) <= 34)))
         {
            this.__setPropDict[this.__id133_] = param1;
            try
            {
               this.__id133_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id133_.betweenRandL = 1150;
            this.__id133_.idx = 4;
            this.__id133_.isBoss = false;
            try
            {
               this.__id133_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id134__主游戏_标志_34(param1:int) : *
      {
         if(this.__id134_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id134_] == undefined || !(int(this.__setPropDict[this.__id134_]) >= 35 && int(this.__setPropDict[this.__id134_]) <= 39)))
         {
            this.__setPropDict[this.__id134_] = param1;
            try
            {
               this.__id134_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id134_.delay = 12;
            this.__id134_.enemyType = 16;
            this.__id134_.interval = 2;
            this.__id134_.isRandom = false;
            this.__id134_.stopPointIdx = 1;
            this.__id134_.totalNum = 5;
            try
            {
               this.__id134_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id134__主游戏_标志_39(param1:int) : *
      {
         if(this.__id134_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id134_] == undefined || !(int(this.__setPropDict[this.__id134_]) >= 40 && int(this.__setPropDict[this.__id134_]) <= 44)))
         {
            this.__setPropDict[this.__id134_] = param1;
            try
            {
               this.__id134_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id134_.delay = 2;
            this.__id134_.enemyType = 19;
            this.__id134_.interval = 2;
            this.__id134_.isRandom = false;
            this.__id134_.stopPointIdx = 4;
            this.__id134_.totalNum = 1;
            try
            {
               this.__id134_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id135__主游戏_标志_34(param1:int) : *
      {
         if(this.__id135_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id135_] == undefined || !(int(this.__setPropDict[this.__id135_]) >= 35 && int(this.__setPropDict[this.__id135_]) <= 39)))
         {
            this.__setPropDict[this.__id135_] = param1;
            try
            {
               this.__id135_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id135_.delay = 12;
            this.__id135_.enemyType = 14;
            this.__id135_.interval = 2;
            this.__id135_.isRandom = false;
            this.__id135_.stopPointIdx = 1;
            this.__id135_.totalNum = 5;
            try
            {
               this.__id135_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id135__主游戏_标志_39(param1:int) : *
      {
         if(this.__id135_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id135_] == undefined || !(int(this.__setPropDict[this.__id135_]) >= 40 && int(this.__setPropDict[this.__id135_]) <= 44)))
         {
            this.__setPropDict[this.__id135_] = param1;
            try
            {
               this.__id135_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id135_.delay = 12;
            this.__id135_.enemyType = 20;
            this.__id135_.interval = 2;
            this.__id135_.isRandom = false;
            this.__id135_.stopPointIdx = 1;
            this.__id135_.totalNum = 5;
            try
            {
               this.__id135_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id136__主游戏_标志_34(param1:int) : *
      {
         if(this.__id136_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id136_] == undefined || !(int(this.__setPropDict[this.__id136_]) >= 35 && int(this.__setPropDict[this.__id136_]) <= 39)))
         {
            this.__setPropDict[this.__id136_] = param1;
            try
            {
               this.__id136_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id136_.betweenRandL = 1150;
            this.__id136_.idx = 3;
            this.__id136_.isBoss = false;
            try
            {
               this.__id136_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id137__主游戏_标志_34(param1:int) : *
      {
         if(this.__id137_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id137_] == undefined || !(int(this.__setPropDict[this.__id137_]) >= 35 && int(this.__setPropDict[this.__id137_]) <= 39)))
         {
            this.__setPropDict[this.__id137_] = param1;
            try
            {
               this.__id137_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id137_.betweenRandL = 1150;
            this.__id137_.idx = 4;
            this.__id137_.isBoss = false;
            try
            {
               this.__id137_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id138__主游戏_标志_34(param1:int) : *
      {
         if(this.__id138_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id138_] == undefined || !(int(this.__setPropDict[this.__id138_]) >= 35 && int(this.__setPropDict[this.__id138_]) <= 39)))
         {
            this.__setPropDict[this.__id138_] = param1;
            try
            {
               this.__id138_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id138_.delay = 2;
            this.__id138_.enemyType = 20;
            this.__id138_.interval = 2;
            this.__id138_.isRandom = false;
            this.__id138_.stopPointIdx = 3;
            this.__id138_.totalNum = 1;
            try
            {
               this.__id138_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id139__主游戏_标志_34(param1:int) : *
      {
         if(this.__id139_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id139_] == undefined || !(int(this.__setPropDict[this.__id139_]) >= 35 && int(this.__setPropDict[this.__id139_]) <= 39)))
         {
            this.__setPropDict[this.__id139_] = param1;
            try
            {
               this.__id139_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id139_.delay = 2;
            this.__id139_.enemyType = 14;
            this.__id139_.interval = 2;
            this.__id139_.isRandom = false;
            this.__id139_.stopPointIdx = 3;
            this.__id139_.totalNum = 3;
            try
            {
               this.__id139_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id140__主游戏_标志_34(param1:int) : *
      {
         if(this.__id140_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id140_] == undefined || !(int(this.__setPropDict[this.__id140_]) >= 35 && int(this.__setPropDict[this.__id140_]) <= 39)))
         {
            this.__setPropDict[this.__id140_] = param1;
            try
            {
               this.__id140_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id140_.delay = 12;
            this.__id140_.enemyType = 16;
            this.__id140_.interval = 2;
            this.__id140_.isRandom = false;
            this.__id140_.stopPointIdx = 3;
            this.__id140_.totalNum = 5;
            try
            {
               this.__id140_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id141__主游戏_标志_34(param1:int) : *
      {
         if(this.__id141_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id141_] == undefined || !(int(this.__setPropDict[this.__id141_]) >= 35 && int(this.__setPropDict[this.__id141_]) <= 39)))
         {
            this.__setPropDict[this.__id141_] = param1;
            try
            {
               this.__id141_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id141_.delay = 12;
            this.__id141_.enemyType = 14;
            this.__id141_.interval = 2;
            this.__id141_.isRandom = false;
            this.__id141_.stopPointIdx = 3;
            this.__id141_.totalNum = 5;
            try
            {
               this.__id141_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id142__主游戏_标志_34(param1:int) : *
      {
         if(this.__id142_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id142_] == undefined || !(int(this.__setPropDict[this.__id142_]) >= 35 && int(this.__setPropDict[this.__id142_]) <= 39)))
         {
            this.__setPropDict[this.__id142_] = param1;
            try
            {
               this.__id142_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id142_.delay = 2;
            this.__id142_.enemyType = 16;
            this.__id142_.interval = 2;
            this.__id142_.isRandom = false;
            this.__id142_.stopPointIdx = 3;
            this.__id142_.totalNum = 3;
            try
            {
               this.__id142_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id143__主游戏_标志_34(param1:int) : *
      {
         if(this.__id143_ != null && param1 >= 35 && param1 <= 39 && (this.__setPropDict[this.__id143_] == undefined || !(int(this.__setPropDict[this.__id143_]) >= 35 && int(this.__setPropDict[this.__id143_]) <= 39)))
         {
            this.__setPropDict[this.__id143_] = param1;
            try
            {
               this.__id143_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id143_.delay = 2;
            this.__id143_.enemyType = 17;
            this.__id143_.interval = 2;
            this.__id143_.isRandom = false;
            this.__id143_.stopPointIdx = 3;
            this.__id143_.totalNum = 1;
            try
            {
               this.__id143_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id144__主游戏_标志_39(param1:int) : *
      {
         if(this.__id144_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id144_] == undefined || !(int(this.__setPropDict[this.__id144_]) >= 40 && int(this.__setPropDict[this.__id144_]) <= 44)))
         {
            this.__setPropDict[this.__id144_] = param1;
            try
            {
               this.__id144_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id144_.betweenRandL = 1150;
            this.__id144_.idx = 2;
            this.__id144_.isBoss = false;
            try
            {
               this.__id144_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id145__主游戏_标志_39(param1:int) : *
      {
         if(this.__id145_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id145_] == undefined || !(int(this.__setPropDict[this.__id145_]) >= 40 && int(this.__setPropDict[this.__id145_]) <= 44)))
         {
            this.__setPropDict[this.__id145_] = param1;
            try
            {
               this.__id145_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id145_.betweenRandL = 1150;
            this.__id145_.idx = 3;
            this.__id145_.isBoss = false;
            try
            {
               this.__id145_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id146__主游戏_标志_39(param1:int) : *
      {
         if(this.__id146_ != null && param1 >= 40 && param1 <= 44 && (this.__setPropDict[this.__id146_] == undefined || !(int(this.__setPropDict[this.__id146_]) >= 40 && int(this.__setPropDict[this.__id146_]) <= 44)))
         {
            this.__setPropDict[this.__id146_] = param1;
            try
            {
               this.__id146_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id146_.betweenRandL = 1150;
            this.__id146_.idx = 4;
            this.__id146_.isBoss = false;
            try
            {
               this.__id146_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id147__主游戏_标志_44(param1:int) : *
      {
         if(this.__id147_ != null && param1 >= 45 && param1 <= 48 && (this.__setPropDict[this.__id147_] == undefined || !(int(this.__setPropDict[this.__id147_]) >= 45 && int(this.__setPropDict[this.__id147_]) <= 48)))
         {
            this.__setPropDict[this.__id147_] = param1;
            try
            {
               this.__id147_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id147_.betweenRandL = 940;
            this.__id147_.idx = 0;
            this.__id147_.isBoss = false;
            try
            {
               this.__id147_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id148__主游戏_标志_54(param1:int) : *
      {
         if(this.__id148_ != null && param1 >= 55 && param1 <= 59 && (this.__setPropDict[this.__id148_] == undefined || !(int(this.__setPropDict[this.__id148_]) >= 55 && int(this.__setPropDict[this.__id148_]) <= 59)))
         {
            this.__setPropDict[this.__id148_] = param1;
            try
            {
               this.__id148_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id148_.betweenRandL = 1150;
            this.__id148_.idx = 4;
            this.__id148_.isBoss = false;
            try
            {
               this.__id148_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp___id149__主游戏_标志_54(param1:int) : *
      {
         if(this.__id149_ != null && param1 >= 55 && param1 <= 59 && (this.__setPropDict[this.__id149_] == undefined || !(int(this.__setPropDict[this.__id149_]) >= 55 && int(this.__setPropDict[this.__id149_]) <= 59)))
         {
            this.__setPropDict[this.__id149_] = param1;
            try
            {
               this.__id149_["componentInspectorSetting"] = true;
            }
            catch(e:Error)
            {
            }
            this.__id149_.delay = 2;
            this.__id149_.enemyType = 26;
            this.__id149_.interval = 2;
            this.__id149_.isRandom = false;
            this.__id149_.stopPointIdx = 0;
            this.__id149_.totalNum = 1;
            try
            {
               this.__id149_["componentInspectorSetting"] = false;
            }
            catch(e:Error)
            {
            }
         }
      }
      
      internal function __setProp_handler(param1:Object) : *
      {
         var _loc2_:int = 0;
         _loc2_ = currentFrame;
         if(this.__lastFrameProp == _loc2_)
         {
            return;
         }
         this.__lastFrameProp = _loc2_;
         this.__setProp___id0__主游戏_标志_0(_loc2_);
         this.__setProp___id0__主游戏_标志_9(_loc2_);
         this.__setProp___id0__主游戏_标志_14(_loc2_);
         this.__setProp___id0__主游戏_标志_19(_loc2_);
         this.__setProp___id0__主游戏_标志_24(_loc2_);
         this.__setProp___id0__主游戏_标志_29(_loc2_);
         this.__setProp___id1__主游戏_标志_0(_loc2_);
         this.__setProp___id1__主游戏_标志_4(_loc2_);
         this.__setProp___id1__主游戏_标志_9(_loc2_);
         this.__setProp___id1__主游戏_标志_14(_loc2_);
         this.__setProp___id1__主游戏_标志_19(_loc2_);
         this.__setProp___id1__主游戏_标志_24(_loc2_);
         this.__setProp___id1__主游戏_标志_29(_loc2_);
         this.__setProp___id1__主游戏_标志_34(_loc2_);
         this.__setProp___id1__主游戏_标志_39(_loc2_);
         this.__setProp___id2__主游戏_标志_0(_loc2_);
         this.__setProp___id2__主游戏_标志_4(_loc2_);
         this.__setProp___id3__主游戏_标志_0(_loc2_);
         this.__setProp___id3__主游戏_标志_4(_loc2_);
         this.__setProp___id4__主游戏_标志_0(_loc2_);
         this.__setProp___id4__主游戏_标志_4(_loc2_);
         this.__setProp___id5__主游戏_标志_0(_loc2_);
         this.__setProp___id5__主游戏_标志_4(_loc2_);
         this.__setProp___id6__主游戏_标志_0(_loc2_);
         this.__setProp___id6__主游戏_标志_4(_loc2_);
         this.__setProp___id7__主游戏_标志_0(_loc2_);
         this.__setProp___id8__主游戏_标志_0(_loc2_);
         this.__setProp___id9__主游戏_标志_0(_loc2_);
         this.__setProp___id10__主游戏_标志_0(_loc2_);
         this.__setProp___id11__主游戏_标志_0(_loc2_);
         this.__setProp___id12__主游戏_标志_0(_loc2_);
         this.__setProp___id13__主游戏_标志_0(_loc2_);
         this.__setProp___id14__主游戏_标志_0(_loc2_);
         this.__setProp___id15__主游戏_标志_0(_loc2_);
         this.__setProp___id16__主游戏_标志_0(_loc2_);
         this.__setProp___id17__主游戏_标志_0(_loc2_);
         this.__setProp___id18__主游戏_标志_0(_loc2_);
         this.__setProp___id19__主游戏_标志_4(_loc2_);
         this.__setProp___id20__主游戏_标志_4(_loc2_);
         this.__setProp___id21__主游戏_标志_4(_loc2_);
         this.__setProp___id22__主游戏_标志_4(_loc2_);
         this.__setProp___id23__主游戏_标志_4(_loc2_);
         this.__setProp___id24__主游戏_标志_4(_loc2_);
         this.__setProp___id25__主游戏_标志_4(_loc2_);
         this.__setProp___id26__主游戏_标志_4(_loc2_);
         this.__setProp___id27__主游戏_标志_4(_loc2_);
         this.__setProp___id28__主游戏_标志_4(_loc2_);
         this.__setProp___id29__主游戏_标志_4(_loc2_);
         this.__setProp___id30__主游戏_标志_4(_loc2_);
         this.__setProp___id31__主游戏_标志_4(_loc2_);
         this.__setProp___id32__主游戏_标志_4(_loc2_);
         this.__setProp___id33__主游戏_标志_4(_loc2_);
         this.__setProp___id34__主游戏_标志_4(_loc2_);
         this.__setProp___id35__主游戏_背景_9(_loc2_);
         this.__setProp___id36__主游戏_背景_9(_loc2_);
         this.__setProp___id37__主游戏_背景_9(_loc2_);
         this.__setProp___id38__主游戏_背景_9(_loc2_);
         this.__setProp___id39__主游戏_背景_9(_loc2_);
         this.__setProp___id40__主游戏_背景_9(_loc2_);
         this.__setProp___id41__主游戏_背景_9(_loc2_);
         this.__setProp___id42__主游戏_背景_9(_loc2_);
         this.__setProp___id43__主游戏_背景_9(_loc2_);
         this.__setProp___id44__主游戏_背景_9(_loc2_);
         this.__setProp___id45__主游戏_背景_9(_loc2_);
         this.__setProp___id46__主游戏_背景_9(_loc2_);
         this.__setProp___id47__主游戏_背景_9(_loc2_);
         this.__setProp___id48__主游戏_背景_9(_loc2_);
         this.__setProp___id49__主游戏_背景_9(_loc2_);
         this.__setProp___id50__主游戏_背景_9(_loc2_);
         this.__setProp___id51__主游戏_背景_9(_loc2_);
         this.__setProp___id52__主游戏_背景_9(_loc2_);
         this.__setProp___id53__主游戏_背景_14(_loc2_);
         this.__setProp___id53__主游戏_背景_19(_loc2_);
         this.__setProp___id54__主游戏_背景_14(_loc2_);
         this.__setProp___id54__主游戏_背景_19(_loc2_);
         this.__setProp___id55__主游戏_背景_14(_loc2_);
         this.__setProp___id55__主游戏_背景_19(_loc2_);
         this.__setProp___id56__主游戏_背景_14(_loc2_);
         this.__setProp___id56__主游戏_背景_19(_loc2_);
         this.__setProp___id57__主游戏_背景_14(_loc2_);
         this.__setProp___id58__主游戏_背景_14(_loc2_);
         this.__setProp___id59__主游戏_背景_14(_loc2_);
         this.__setProp___id60__主游戏_背景_14(_loc2_);
         this.__setProp___id61__主游戏_背景_14(_loc2_);
         this.__setProp___id62__主游戏_背景_14(_loc2_);
         this.__setProp___id63__主游戏_背景_14(_loc2_);
         this.__setProp___id64__主游戏_背景_14(_loc2_);
         this.__setProp___id65__主游戏_背景_14(_loc2_);
         this.__setProp___id65__主游戏_背景_19(_loc2_);
         this.__setProp___id66__主游戏_背景_14(_loc2_);
         this.__setProp___id66__主游戏_背景_24(_loc2_);
         this.__setProp___id67__主游戏_背景_14(_loc2_);
         this.__setProp___id67__主游戏_背景_24(_loc2_);
         this.__setProp___id68__主游戏_背景_14(_loc2_);
         this.__setProp___id68__主游戏_背景_24(_loc2_);
         this.__setProp___id69__主游戏_背景_14(_loc2_);
         this.__setProp___id69__主游戏_背景_24(_loc2_);
         this.__setProp___id70__主游戏_背景_14(_loc2_);
         this.__setProp___id70__主游戏_背景_24(_loc2_);
         this.__setProp___id71__主游戏_背景_14(_loc2_);
         this.__setProp___id71__主游戏_背景_24(_loc2_);
         this.__setProp___id72__主游戏_背景_14(_loc2_);
         this.__setProp___id72__主游戏_背景_19(_loc2_);
         this.__setProp___id72__主游戏_背景_24(_loc2_);
         this.__setProp___id73__主游戏_背景_14(_loc2_);
         this.__setProp___id73__主游戏_背景_19(_loc2_);
         this.__setProp___id73__主游戏_背景_24(_loc2_);
         this.__setProp___id74__主游戏_背景_14(_loc2_);
         this.__setProp___id74__主游戏_背景_19(_loc2_);
         this.__setProp___id74__主游戏_背景_24(_loc2_);
         this.__setProp___id75__主游戏_背景_14(_loc2_);
         this.__setProp___id75__主游戏_背景_19(_loc2_);
         this.__setProp___id76__主游戏_背景_14(_loc2_);
         this.__setProp___id76__主游戏_背景_19(_loc2_);
         this.__setProp___id77__主游戏_背景_14(_loc2_);
         this.__setProp___id77__主游戏_背景_19(_loc2_);
         this.__setProp___id78__主游戏_背景_14(_loc2_);
         this.__setProp___id78__主游戏_背景_19(_loc2_);
         this.__setProp___id79__主游戏_背景_14(_loc2_);
         this.__setProp___id79__主游戏_背景_19(_loc2_);
         this.__setProp___id80__主游戏_背景_14(_loc2_);
         this.__setProp___id80__主游戏_背景_24(_loc2_);
         this.__setProp___id81__主游戏_背景_14(_loc2_);
         this.__setProp___id81__主游戏_背景_24(_loc2_);
         this.__setProp___id82__主游戏_背景_14(_loc2_);
         this.__setProp___id82__主游戏_背景_24(_loc2_);
         this.__setProp___id83__主游戏_背景_14(_loc2_);
         this.__setProp___id83__主游戏_背景_24(_loc2_);
         this.__setProp___id84__主游戏_背景_14(_loc2_);
         this.__setProp___id84__主游戏_背景_24(_loc2_);
         this.__setProp___id85__主游戏_背景_14(_loc2_);
         this.__setProp___id85__主游戏_背景_24(_loc2_);
         this.__setProp___id86__主游戏_背景_14(_loc2_);
         this.__setProp___id86__主游戏_背景_24(_loc2_);
         this.__setProp___id87__主游戏_背景_14(_loc2_);
         this.__setProp___id87__主游戏_背景_24(_loc2_);
         this.__setProp___id88__主游戏_标志_14(_loc2_);
         this.__setProp___id88__主游戏_标志_19(_loc2_);
         this.__setProp___id88__主游戏_标志_24(_loc2_);
         this.__setProp___id88__主游戏_标志_29(_loc2_);
         this.__setProp___id88__主游戏_标志_34(_loc2_);
         this.__setProp___id88__主游戏_标志_39(_loc2_);
         this.__setProp___id88__主游戏_标志_44(_loc2_);
         this.__setProp___id89__主游戏_标志_14(_loc2_);
         this.__setProp___id89__主游戏_标志_19(_loc2_);
         this.__setProp___id89__主游戏_标志_24(_loc2_);
         this.__setProp___id90__主游戏_标志_14(_loc2_);
         this.__setProp___id90__主游戏_标志_19(_loc2_);
         this.__setProp___id90__主游戏_标志_24(_loc2_);
         this.__setProp___id91__主游戏_标志_14(_loc2_);
         this.__setProp___id91__主游戏_标志_19(_loc2_);
         this.__setProp___id91__主游戏_标志_24(_loc2_);
         this.__setProp___id92__主游戏_标志_14(_loc2_);
         this.__setProp___id92__主游戏_标志_19(_loc2_);
         this.__setProp___id92__主游戏_标志_24(_loc2_);
         this.__setProp___id93__主游戏_标志_14(_loc2_);
         this.__setProp___id93__主游戏_标志_19(_loc2_);
         this.__setProp___id93__主游戏_标志_24(_loc2_);
         this.__setProp___id94__主游戏_标志_14(_loc2_);
         this.__setProp___id94__主游戏_标志_19(_loc2_);
         this.__setProp___id94__主游戏_标志_24(_loc2_);
         this.__setProp___id95__主游戏_标志_14(_loc2_);
         this.__setProp___id95__主游戏_标志_19(_loc2_);
         this.__setProp___id95__主游戏_标志_24(_loc2_);
         this.__setProp___id96__主游戏_标志_14(_loc2_);
         this.__setProp___id96__主游戏_标志_19(_loc2_);
         this.__setProp___id97__主游戏_标志_14(_loc2_);
         this.__setProp___id97__主游戏_标志_19(_loc2_);
         this.__setProp___id98__主游戏_标志_14(_loc2_);
         this.__setProp___id98__主游戏_标志_19(_loc2_);
         this.__setProp___id99__主游戏_标志_14(_loc2_);
         this.__setProp___id99__主游戏_标志_19(_loc2_);
         this.__setProp___id100__主游戏_标志_14(_loc2_);
         this.__setProp___id101__主游戏_标志_14(_loc2_);
         this.__setProp___id102__主游戏_标志_14(_loc2_);
         this.__setProp___id103__主游戏_标志_14(_loc2_);
         this.__setProp___id104__主游戏_标志_14(_loc2_);
         this.__setProp___id105__主游戏_标志_14(_loc2_);
         this.__setProp___id106__主游戏_标志_14(_loc2_);
         this.__setProp___id107__主游戏_标志_14(_loc2_);
         this.__setProp___id108__主游戏_标志_19(_loc2_);
         this.__setProp___id108__主游戏_标志_44(_loc2_);
         this.__setProp___id109__主游戏_标志_19(_loc2_);
         this.__setProp___id109__主游戏_标志_24(_loc2_);
         this.__setProp___id109__主游戏_标志_29(_loc2_);
         this.__setProp___id109__主游戏_标志_34(_loc2_);
         this.__setProp___id109__主游戏_标志_44(_loc2_);
         this.__setProp___id110__主游戏_标志_19(_loc2_);
         this.__setProp___id110__主游戏_标志_24(_loc2_);
         this.__setProp___id110__主游戏_标志_29(_loc2_);
         this.__setProp___id110__主游戏_标志_34(_loc2_);
         this.__setProp___id110__主游戏_标志_39(_loc2_);
         this.__setProp___id110__主游戏_标志_44(_loc2_);
         this.__setProp___id111__主游戏_标志_19(_loc2_);
         this.__setProp___id111__主游戏_标志_24(_loc2_);
         this.__setProp___id111__主游戏_标志_29(_loc2_);
         this.__setProp___id111__主游戏_标志_34(_loc2_);
         this.__setProp___id111__主游戏_标志_39(_loc2_);
         this.__setProp___id112__主游戏_标志_19(_loc2_);
         this.__setProp___id112__主游戏_标志_24(_loc2_);
         this.__setProp___id112__主游戏_标志_29(_loc2_);
         this.__setProp___id112__主游戏_标志_34(_loc2_);
         this.__setProp___id112__主游戏_标志_39(_loc2_);
         this.__setProp___id113__主游戏_标志_19(_loc2_);
         this.__setProp___id113__主游戏_标志_24(_loc2_);
         this.__setProp___id113__主游戏_标志_29(_loc2_);
         this.__setProp___id113__主游戏_标志_34(_loc2_);
         this.__setProp___id113__主游戏_标志_39(_loc2_);
         this.__setProp___id114__主游戏_标志_19(_loc2_);
         this.__setProp___id114__主游戏_标志_24(_loc2_);
         this.__setProp___id114__主游戏_标志_29(_loc2_);
         this.__setProp___id114__主游戏_标志_34(_loc2_);
         this.__setProp___id114__主游戏_标志_39(_loc2_);
         this.__setProp___id115__主游戏_标志_19(_loc2_);
         this.__setProp___id115__主游戏_标志_24(_loc2_);
         this.__setProp___id115__主游戏_标志_29(_loc2_);
         this.__setProp___id115__主游戏_标志_34(_loc2_);
         this.__setProp___id115__主游戏_标志_39(_loc2_);
         this.__setProp___id116__主游戏_标志_19(_loc2_);
         this.__setProp___id116__主游戏_标志_24(_loc2_);
         this.__setProp___id117__主游戏_标志_19(_loc2_);
         this.__setProp___id118__主游戏_标志_19(_loc2_);
         this.__setProp___id123__主游戏_标志_24(_loc2_);
         this.__setProp___id124__主游戏_标志_24(_loc2_);
         this.__setProp___id124__主游戏_标志_29(_loc2_);
         this.__setProp___id124__主游戏_标志_34(_loc2_);
         this.__setProp___id124__主游戏_标志_39(_loc2_);
         this.__setProp___id125__主游戏_标志_24(_loc2_);
         this.__setProp___id125__主游戏_标志_29(_loc2_);
         this.__setProp___id125__主游戏_标志_39(_loc2_);
         this.__setProp___id126__主游戏_标志_24(_loc2_);
         this.__setProp___id126__主游戏_标志_29(_loc2_);
         this.__setProp___id126__主游戏_标志_34(_loc2_);
         this.__setProp___id126__主游戏_标志_39(_loc2_);
         this.__setProp___id127__主游戏_标志_24(_loc2_);
         this.__setProp___id127__主游戏_标志_29(_loc2_);
         this.__setProp___id127__主游戏_标志_34(_loc2_);
         this.__setProp___id127__主游戏_标志_39(_loc2_);
         this.__setProp___id128__主游戏_标志_24(_loc2_);
         this.__setProp___id128__主游戏_标志_29(_loc2_);
         this.__setProp___id128__主游戏_标志_34(_loc2_);
         this.__setProp___id129__主游戏_标志_24(_loc2_);
         this.__setProp___id129__主游戏_标志_29(_loc2_);
         this.__setProp___id129__主游戏_标志_34(_loc2_);
         this.__setProp___id130__主游戏_标志_24(_loc2_);
         this.__setProp___id131__主游戏_标志_29(_loc2_);
         this.__setProp___id132__主游戏_标志_29(_loc2_);
         this.__setProp___id132__主游戏_标志_34(_loc2_);
         this.__setProp___id132__主游戏_标志_39(_loc2_);
         this.__setProp___id133__主游戏_标志_29(_loc2_);
         this.__setProp___id134__主游戏_标志_34(_loc2_);
         this.__setProp___id134__主游戏_标志_39(_loc2_);
         this.__setProp___id135__主游戏_标志_34(_loc2_);
         this.__setProp___id135__主游戏_标志_39(_loc2_);
         this.__setProp___id136__主游戏_标志_34(_loc2_);
         this.__setProp___id137__主游戏_标志_34(_loc2_);
         this.__setProp___id138__主游戏_标志_34(_loc2_);
         this.__setProp___id139__主游戏_标志_34(_loc2_);
         this.__setProp___id140__主游戏_标志_34(_loc2_);
         this.__setProp___id141__主游戏_标志_34(_loc2_);
         this.__setProp___id142__主游戏_标志_34(_loc2_);
         this.__setProp___id143__主游戏_标志_34(_loc2_);
         this.__setProp___id144__主游戏_标志_39(_loc2_);
         this.__setProp___id145__主游戏_标志_39(_loc2_);
         this.__setProp___id146__主游戏_标志_39(_loc2_);
         this.__setProp___id147__主游戏_标志_44(_loc2_);
         this.__setProp___id148__主游戏_标志_54(_loc2_);
         this.__setProp___id149__主游戏_标志_54(_loc2_);
      }
   }
}

