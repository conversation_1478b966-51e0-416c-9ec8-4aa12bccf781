package com.greensock.plugins
{
   import com.greensock.*;
   
   public class EndArrayPlugin extends TweenPlugin
   {
      public static const API:Number = 1;
      
      protected var _a:Array;
      
      protected var _info:Array;
      
      public function EndArrayPlugin()
      {
         super();
         this._info = [];
         this.propName = "endArray";
         this.overwriteProps = ["endArray"];
      }
      
      public function init(param1:Array, param2:Array) : void
      {
         this._a = param1;
         var _loc3_:* = param2.length;
         while(_loc3_--)
         {
            if(param1[_loc3_] != param2[_loc3_] && param1[_loc3_] != null)
            {
               this._info[this._info.length] = new ArrayTweenInfo(_loc3_,this._a[_loc3_],param2[_loc3_] - this._a[_loc3_]);
            }
         }
      }
      
      override public function onInitTween(param1:Object, param2:*, param3:TweenLite) : Boolean
      {
         if(!(param1 is Array) || !(param2 is Array))
         {
            return false;
         }
         this.init(param1 as Array,param2);
         return true;
      }
      
      override public function set changeFactor(param1:Number) : void
      {
         var _loc2_:ArrayTweenInfo = null;
         var _loc3_:Number = Number(NaN);
         var _loc4_:* = this._info.length;
         if(this.round)
         {
            while(_loc4_--)
            {
               _loc2_ = this._info[_loc4_];
               _loc3_ = _loc2_.start + _loc2_.change * param1;
               this._a[_loc2_.index] = _loc3_ > 0 ? int(_loc3_ + 0.5) : int(_loc3_ - 0.5);
            }
         }
         else
         {
            while(_loc4_--)
            {
               _loc2_ = this._info[_loc4_];
               this._a[_loc2_.index] = _loc2_.start + _loc2_.change * param1;
            }
         }
      }
   }
}

class ArrayTweenInfo
{
   public var change:Number;
   
   public var start:Number;
   
   public var index:uint;
   
   public function ArrayTweenInfo(param1:uint, param2:Number, param3:Number)
   {
      super();
      this.index = param1;
      this.start = param2;
      this.change = param3;
   }
}
