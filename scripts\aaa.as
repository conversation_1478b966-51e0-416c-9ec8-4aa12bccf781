package
{
   import flash.text.Font;
   
   [Embed(source="/_assets/1_aaa_方正粗圆简体.ttf",
   fontName="方正粗圆简体",
   fontFamily="方正粗圆简体",
   mimeType="application/x-font",
   fontWeight="normal",
   fontStyle="normal",
   unicodeRange="U+0020-007E,U+00A0-00A0,U+00B7-00B7,U+2014-2014,U+2018-2019,U+201C-201D,U+2026-2026,U+2030-2030,U+20AC-20AC,U+3001-3001,U+4E00-4E00,U+4E03-4E03,U+4E07-4E07,U+4E09-4E0A,U+4E0D-4E0E,U+4E1C-4E1C,U+4E1E-4E1E,U+4E39-4E39,U+4E3D-4E3D,U+4E4B-4E4B,U+4E5D-4E5D,U+4E86-4E86,U+4E8E-4E8E,U+4E91-4E91,U+4E94-4E94,U+4EA1-4EA1,U+4EBA-4EBA,U+4EE4-4EE4,U+4EF7-4EF7,U+4F18-4F18,U+4F1A-4F1A,U+4F20-4F20,U+4F3C-4F3C,U+4F53-4F53,U+4F69-4F69,U+4FDD-4FDD,U+4FF1-4FF1,U+503C-503C,U+5143-5143,U+5149-5149,U+5174-5174,U+5176-5177,U+517D-517D,U+5185-5185,U+51A4-51A4,U+51B0-51B0,U+51D1-51D1,U+51FA-51FB,U+5200-5200,U+5236-5236,U+524D-524D,U+5251-5251,U+529B-529B,U+529F-52A0,U+52AB-52AB,U+52BF-52BF,U+5316-5317,U+5343-5343,U+534E-534E,U+53BB-53BB,U+53D1-53D1,U+53D6-53D6,U+53E4-53E4,U+53EA-53EA,U+53EF-53EF,U+53F2-53F2,U+53F7-53F7,U+540C-540C,U+540E-540E,U+5438-5438,U+5448-5448,U+5473-5473,U+547D-547D,U+5486-5486,U+548C-548C,U+54C1-54C1,U+54EE-54EE,U+5668-5668,U+56DE-56DE,U+56FA-56FA,U+5706-5706,U+5723-5723,U+5728-5728,U+5730-5730,U+575A-575A,U+578B-578B,U+582A-582A,U+589E-589E,U+58EB-58EB,U+58F0-58F0,U+58F3-58F3,U+5907-5907,U+590D-590D,U+5927-5927,U+5929-5929,U+5931-5931,U+5982-5982,U+5996-5996,U+5B58-5B58,U+5B88-5B88,U+5B8C-5B8C,U+5B9A-5B9B,U+5B9D-5B9D,U+5BAB-5BAB,U+5BB6-5BB6,U+5BD2-5BD2,U+5C16-5C16,U+5E08-5E08,U+5E10-5E10,U+5E61-5E61,U+5E74-5E74,U+5EA6-5EA6,U+5F00-5F00,U+5F02-5F02,U+5F52-5F53,U+5F71-5F71,U+5F85-5F85,U+5F97-5F97,U+5FA1-5FA1,U+5FC3-5FC3,U+5FC6-5FC6,U+5FD8-5FD8,U+6028-6028,U+602A-602A,U+606F-606F,U+60B2-60B2,U+610F-610F,U+6210-6210,U+6212-6212,U+6218-6218,U+621F-621F,U+6234-6234,U+624B-624B,U+624D-624D,U+6253-6253,U+62A4-62A4,U+62DB-62DB,U+62E8-62E8,U+6307-6307,U+6325-6325,U+6346-6346,U+636E-636E,U+6447-6447,U+6467-6467,U+6495-6495,U+653B-653B,U+653E-653E,U+6563-6563,U+656C-656C,U+6570-6570,U+6597-6597,U+65A4-65A4,U+65BD-65BD,U+65D7-65D7,U+65E0-65E0,U+65E5-65E5,U+65F6-65F6,U+660E-660E,U+661F-661F,U+662F-662F,U+666E-666E,U+6682-6682,U+66B4-66B4,U+6708-6709,U+670D-670D,U+671B-671B,U+671F-671F,U+6728-6728,U+672A-672A,U+6731-6731,U+6743-6743,U+6756-6756,U+6765-6765,U+677E-677E,U+67A2-67A2,U+67D3-67D3,U+6863-6863,U+68CD-68CD,U+68D2-68D2,U+6B64-6B64,U+6B66-6B66,U+6B7B-6B7B,U+6BCF-6BCF,U+6BD4-6BD4,U+6BDB-6BDB,U+6C14-6C14,U+6C60-6C60,U+6C64-6C64,U+6CD5-6CD5,U+6CE1-6CE1,U+6D69-6D6A,U+6D77-6D78,U+6E21-6E21,U+6E90-6E90,U+706B-706B,U+706D-706D,U+7075-7075,U+70BC-70BC,U+70C1-70C1,U+715E-715E,U+7194-7194,U+7247-7247,U+7259-7259,U+725B-725B,U+7269-7269,U+72B9-72B9,U+7334-7334,U+733F-733F,U+7384-7384,U+7387-7387,U+7389-7389,U+738B-738B,U+7391-7391,U+73AF-73B0,U+7422-7422,U+7487-7487,U+751F-751F,U+7528-7528,U+7531-7532,U+7535-7535,U+754C-754C,U+75BE-75BE,U+767B-767B,U+767D-767E,U+7684-7684,U+7686-7687,U+7693-7693,U+76F8-76F8,U+770B-770B,U+771F-771F,U+7740-7740,U+77E5-77E5,U+77F3-77F3,U+78D0-78D0,U+795E-795E,U+7985-7985,U+79C0-79C0,U+79D2-79D2,U+79D8-79D8,U+7A7F-7A7F,U+7AE5-7AE5,U+7AEF-7AEF,U+7B8D-7B8D,U+7C7B-7C7B,U+7C97-7C97,U+7CBE-7CBE,U+7CD6-7CD6,U+7CD9-7CD9,U+7EC7-7EC7,U+7ECF-7ECF,U+7ED1-7ED2,U+7ED9-7ED9,U+7EDD-7EDD,U+7F16-7F16,U+7FBD-7FBD,U+7FD4-7FD4,U+7FF1-7FF1,U+8000-8000,U+8005-8005,U+800C-800C,U+8033-8033,U+8046-8046,U+805A-805A,U+808B-808B,U+80A1-80A1,U+80BA-80BA,U+80C6-80C6,U+80FD-80FD,U+817E-817E,U+81C2-81C2,U+821E-821E,U+822C-822C,U+826F-826F,U+8292-8292,U+82A6-82A6,U+82CF-82CF,U+82E5-82E5,U+83B7-83B7,U+846B-846B,U+8574-8574,U+85CF-85CF,U+864E-864E,U+8840-8840,U+884C-884C,U+8861-8861,U+8863-8863,U+8888-8888,U+888D-888D,U+88AB-88AB,U+88C2-88C2,U+88C5-88C5,U+88DF-88DF,U+88F3-88F3,U+892A-892A,U+8955-8955,U+89C1-89C1,U+8BB0-8BB0,U+8BD7-8BD7,U+8BE1-8BE1,U+8BF4-8BF4,U+8BF7-8BF7,U+8D22-8D22,U+8D25-8D25,U+8D28-8D28,U+8D2F-8D2F,U+8D66-8D66,U+8D77-8D77,U+8F7D-8F7D,U+8FD9-8FD9,U+8FDB-8FDB,U+8FF9-8FF9,U+901A-901A,U+9020-9020,U+9057-9057,U+907F-907F,U+90AA-90AA,U+91CA-91CA,U+91CC-91CC,U+91CF-91CF,U+91D1-91D1,U+9488-9488,U+94A2-94A2,U+94C3-94C3,U+94DB-94DB,U+94E0-94E0,U+94F8-94F8,U+94FE-94FE,U+9526-9526,U+953B-953B,U+956F-956F,U+95EA-95EA,U+95F4-95F4,U+9632-9633,U+9644-9646,U+9650-9650,U+96C0-96C0,U+96C6-96C6,U+96F7-96F7,U+96FE-96FE,U+9752-9752,U+97F3-97F3,U+9879-9879,U+98CE-98CE,U+9970-9970,U+9A71-9A71,U+9A7E-9A7E,U+9AA8-9AA8,U+9AB8-9AB8,U+9B42-9B42,U+9B54-9B54,U+9C9C-9C9C,U+9CB2-9CB2,U+9CDE-9CDE,U+9E23-9E23,U+9E4F-9E4F,U+9E92-9E92,U+9E9F-9E9F,U+9EC4-9EC4,U+9F13-9F13,U+9F50-9F50,U+9F99-9F99,U+9F9F-9F9F,U+FF0C-FF0C",
   advancedAntiAliasing="true",
   embedAsCFF="false"
   )]
   public dynamic class aaa extends Font
   {
      public function aaa()
      {
         super();
      }
   }
}

